﻿using EmbeddedEmails.Core.Models;
using EmbeddedEmails.Core.Repositories;
using EmbeddedEmails.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EmbeddedEmails.DbStorage.Repositories
{
    public class ZoneRepository : IZoneRepository
    {
        private readonly MasterDbContext db;

        public ZoneRepository(MasterDbContext db)
        {
            this.db = db;
        }

        public async Task<List<EntityMin>> GetZonesviaEmployee(long companyId)
        {
            return await db.Employee.Where(z => z.CompanyId == companyId && z.Region.ZoneId.HasValue && !z.IsDeactive).Select(z => new EntityMin
            {
                Id = z.Region.Zone.Id,
                Name = z.Region.Zone.Name
            }).Distinct().ToListAsync();
        }
    }
}
