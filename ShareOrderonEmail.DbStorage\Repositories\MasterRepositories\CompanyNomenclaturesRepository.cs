﻿using ShareOrderonEmail.Core.Models;
using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using ShareOrderonEmail.DbStorage.DbContexts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ShareOrderonEmail.DbStorage.Repositories.MasterRepositories
{
    public class CompanyNomenclaturesRepository : INomenclatureRepository
    {
        private readonly MasterDbContext db;

        public CompanyNomenclaturesRepository(MasterDbContext db)
        {
            this.db = db;
        }
        public IEnumerable<CompanyNomenclatureMin> GetMins(long companyId)
        {
            var nomenclatures = db.CompanyNomenclatureMapping.Where(c => c.CompanyId == companyId).Select(a => new CompanyNomenclatureMin
            {
                DisplayName = a.Name,
                Id = a.Id,
                NomenclatureId = a.NomenclatureId,
                Helptext = a.HelpText
            }).ToList();

            if (nomenclatures == null || nomenclatures.Count() == 0)
            {
                return null;
            }



            return nomenclatures;

        }
    }
}
