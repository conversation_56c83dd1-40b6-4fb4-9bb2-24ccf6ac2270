﻿using ShareOrderonEmail.Core.Models;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.Core.Repositories.IMasterRepositories
{
    public interface IEmployeeRepository
    {
        Task<Dictionary<long, EmployeeDetails>> GetEmployeeDetails(long companyId, List<long> EmployeeIds);

        Task<Dictionary<long, VanSalesInvoiceDetailsDto>> GetVanSalesInvoiceData(List<long> attendanceId);
    }
}
