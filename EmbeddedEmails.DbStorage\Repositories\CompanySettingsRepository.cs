﻿using EmbeddedEmails.Core.Repositories;
using EmbeddedEmails.DbStorage.DbContexts;
using Libraries.CommonEnums;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;

namespace EmbeddedEmails.DbStorage.Repositories
{
    public class CompanySettingsRepository : ICompanySettingsRepository
    {
        private readonly MasterDbContext db;

        public CompanySettingsRepository(MasterDbContext db)
        {
            this.db = db;
        }

        public virtual Dictionary<string, object> GetSettings(long companyId)
        {
            var settingsdb = db.CompanySettings.Where(s => !s.IsDeprecated)
                .GroupJoin(db.CompanySettingValues.Where(v => v.CompanyId == companyId).DefaultIfEmpty(),
                s => s.Id, v => v.SettingId, (s, v) => new { s.Setting<PERSON>ey, s.SettingType, s.DefaultValue, v.FirstOrDefault().SettingValue })
                .ToList();
            var settings = settingsdb.ToDictionary(k => k.Set<PERSON>, v => ConvertFrom(v.SettingType, v?.SettingValue, v.DefaultValue));
            return settings;

        }

        private static object ConvertFrom(CompanySettingType settingType, string settingValue, string defaultValue)
        {

            switch (settingType)
            {
                case CompanySettingType.Boolean:
                    bool boolValue;
                    if (!string.IsNullOrWhiteSpace(settingValue) && bool.TryParse(settingValue, out boolValue))
                        return boolValue;
                    else
                        return bool.Parse(defaultValue);
                case CompanySettingType.Decimal:
                    double decimalValue;
                    if (!string.IsNullOrWhiteSpace(settingValue) && double.TryParse(settingValue, out decimalValue))
                        return decimalValue;
                    else
                        return double.Parse(defaultValue);
                case CompanySettingType.Integer:
                    long intValue;
                    if (!string.IsNullOrWhiteSpace(settingValue) && long.TryParse(settingValue, out intValue))
                        return intValue;
                    else
                        return int.Parse(defaultValue);
                case CompanySettingType.TextList:
                    if (string.IsNullOrWhiteSpace(settingValue)) return new List<string>();
                    List<string> stringlist = JsonConvert.DeserializeObject<List<string>>(settingValue);
                    return stringlist;
                case CompanySettingType.Text:
                default:
                    return settingValue;

            }

        }


    }
}
