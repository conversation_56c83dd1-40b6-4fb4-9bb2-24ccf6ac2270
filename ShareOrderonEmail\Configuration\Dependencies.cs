﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using ShareOrderonEmail.DbStorage.DbContexts;
using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using ShareOrderonEmail.DbStorage.Repositories.MasterRepositories;
using ShareOrderonEmail.Core.Repositories.ITransactionRepositories;
using ShareOrderonEmail.DbStorage.Repositories.TransactionRepositories;
using ShareOrderonEmail.Core.Services;
using Library.EmailService;
using FileGenerator.Interfaces;
using ShareOrderonEmail.Core.Helpers;
using Library.StorageWriter;
using Library.SlackService;

namespace ShareOrderonEmail.Configuration
{
    public static class Dependencies
    {

        public static void SetUp(IServiceCollection serviceProvider, IConfiguration configuration)
        {

            serviceProvider.AddDbContext<MasterDbContext>(options => options.UseSqlServer(configuration.GetConnectionString("MasterDbConnectionString"))
           , ServiceLifetime.Transient);
            serviceProvider.AddDbContext<TransactionDbContext>(options => options.UseSqlServer(configuration.GetConnectionString("TransactionDbConnectionString"))
           , ServiceLifetime.Transient);
            var masterStorageConnectionString = configuration.GetConnectionString("MasterStorageConnectionString");
            serviceProvider.AddScoped<ICompanyRepository, CompanyRepository>();
            serviceProvider.AddScoped<IBeatRepository, BeatRepository>();
            serviceProvider.AddScoped<IDistributorRepository, DistributorRepositor>();
            serviceProvider.AddScoped<IVanRepository, VanRepository>();
            serviceProvider.AddScoped<IEmployeeRepository, EmployeeRepository>();
            serviceProvider.AddScoped<ILocationRepository, LocationRepository>();
            serviceProvider.AddScoped<IProductRepository, ProductRepository>();
            serviceProvider.AddScoped<IAttendanceRepository, AttendanceRepository>();
            serviceProvider.AddScoped<ISaleRepository, SaleRepository>();
            serviceProvider.AddScoped<INomenclatureSpecifier, Nomenclature>();
            serviceProvider.AddScoped<INomenclatureRepository, CompanyNomenclaturesRepository>();
            serviceProvider.AddScoped<ISchemeSaleRepository, SchemeSaleRepository>();
            serviceProvider.AddScoped<IDbReceivedPaymentRepository, DbRecievedPaymentRepository>();
            serviceProvider.AddScoped<DumpReportBlobWriter>(d => new DumpReportBlobWriter(masterStorageConnectionString));
            serviceProvider.AddSingleton<IConfiguration>(c => configuration);
            serviceProvider.AddSingleton<ShareOrderonEmail>();
            serviceProvider.AddSingleton<ShareOrderonEmailService>();
            serviceProvider.AddSingleton<ShareOrderonEmail>();
            serviceProvider.AddSingleton(s => new ErrorMessenger(masterStorageConnectionString, "ShareOrderViaEmail", "#shareorderviaemail"));
            serviceProvider.AddSingleton(e => new EmailSender(masterStorageConnectionString, Library.Infrastructure.QueueService.QueueType.SendEmbeddedEmail));

        }
    }
}
