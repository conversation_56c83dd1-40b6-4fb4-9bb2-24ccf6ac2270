﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.DbStorage.DbModels.TransactionDbModels
{
    public class Sale
    {
        public long Id { get; set; }

        public long AttendanceId { get; set; }

        public double Discount { get; set; }

        public long? DistributorId { get; set; }

        public bool IsPromoted { get; set; }

        public bool IsRecommended { get; set; }

        public bool IsTopSelling { get; set; }

        public bool IsZonalPromoted { get; set; }

        public double? NearExpiryStockQuantity { get; set; }

        public string OrderType { get; set; }

        public double OriginalPTR { get; set; }

        public double PTR { get; set; }
        public double? MRP { get; set; }

        public long ProductId { get; set; }

        public double ReturnQuantity { get; set; }

        public string ReturnReason { get; set; }

        public double SaleValue { get; set; }

        public decimal SchemeCashDiscount { get; set; }

        public double SchemeQuantity { get; set; }

        public bool StockChange { get; set; }

        public double? StockQuantity { get; set; }

        public double? SuggestedQuantity { get; set; }

        public string DisplayCategory { get; set; }

        public bool IsFocused { get; set; }

        public string NoSalesReason { get; set; }

        public bool IsProductMustSell { get; set; }

        public double? DistributorCashDiscount { get; set; }
        public long? PrimaryCategoryId { get; set; }

        public long? SecondaryCategoryId { get; set; }

        public long? CompanyId { get; set; }
        public long? VAT { get; set; }

        public double StandardUnitConversionFactor { get; set; }
        public double? SuperUnitConversionFactor { get; set; }
        public Attendance Attendance { get; set; }
        public decimal? SGST { get; internal set; }
        public decimal? IGST { get; internal set; }
        public decimal? CGST { get; internal set; }
        public string FAUnifySource { get; set; }
    }
}
