﻿using Libraries.CommonEnums;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace EmbeddedEmails.Core.Models
{
    public class UserUIPreferenceModel
    {
        public long Id { get; set; }
        public long UserId { get; set; }
        public PortalUserRole UserRole { get; set; }
        public UIViewName UIViewName { get; set; }
        public string PreferenceJsonSource { get; set; }
        public long CompanyId { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public bool CanEmail { get; set; }
        public bool IsSummarized { get; set; }
        public bool IsPrimaryCategoryData { get; set; }
    }
    public enum UIViewName
    {
        Unknown = 0,
        EmployeeSummary = 1
    }
}
