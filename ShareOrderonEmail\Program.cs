﻿using ShareOrderonEmail.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.AzureKeyVault;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Azure.KeyVault;
using Microsoft.Azure.Services.AppAuthentication;
using System;
using System.Threading.Tasks;

namespace ShareOrderonEmail
{
    class Program
    {
        public static async Task Main(string[] args)
        {

            IServiceCollection serviceCollection = new ServiceCollection();



            var configBuilder = new ConfigurationBuilder()
               .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
               .AddEnvironmentVariables();




            var keyVaultEndpoint = Environment.GetEnvironmentVariable("KEYVAULT_ENDPOINT");
            if (!string.IsNullOrEmpty(keyVaultEndpoint))
            {
                var azureServiceTokenProvider = new AzureServiceTokenProvider();
                var keyVaultClient = new KeyVaultClient(
                    new KeyVaultClient.AuthenticationCallback(
                        azureServiceTokenProvider.KeyVaultTokenCallback));
                configBuilder.AddAzureKeyVault(
                keyVaultEndpoint, keyVaultClient, new DefaultKeyVaultSecretManager());
            }

            var configuration = configBuilder.Build();
            Dependencies.SetUp(serviceCollection, configuration);
            var serviceProvider = serviceCollection.BuildServiceProvider();

            await serviceProvider.GetRequiredService<ShareOrderonEmail>().Process();

        }
    }
}
