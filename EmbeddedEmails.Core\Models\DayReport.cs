﻿using System;
using System.Collections.Generic;
using System.Text;

namespace EmbeddedEmails.Core.Models
{
    public class DayReport
    {
        public string Name { set; get; }

        public string Date { set; get; }



        public string SaleValue { set; get; }

        public string MTD { set; get; }

        public string LMTD { set; get; }


        public string SaleCrementString { set; get; }

        public string SaleCrementValue { set; get; }

        public string SaleCrementPercent { set; get; }

        public string SaleColor { set; get; }

        public string SaleImage { set; get; }

        public string SaleProgBarCurrentWidth { set; get; }

        public string SaleProgBarMaxWidth { set; get; }



        public long TotalUsers { set; get; }

        public long ActiveUsers { set; get; }

        public long RetailingUsers { set; get; }

        public long MaxActiveUsers { set; get; }

        public string UserCrementString { set; get; }

        public string UserCrementValue { set; get; }

        public string UserCrementPercent { set; get; }

        public string UserColor { set; get; }

        public string UserImage { set; get; }

        public string UserProgBarCurrentWidth { set; get; }

        public string UserProgBarMaxWidth { set; get; }
        public string PCImage { get; set; }
        public string PCColor { get; set; }
        public string PCCrementString { get; set; }
        public string ProdCur { get; set; }
        public string ProdMax { get; set; }
        public string PCCrementPercent { get; set; }
        public int SC { get; set; }
        public int TC { get; set; }
        public int PC { get; set; }
        public string ProdCurBar { get; internal set; }
        public string ProdMaxBar { get; internal set; }
    }
}
