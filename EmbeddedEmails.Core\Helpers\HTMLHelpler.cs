﻿using FileGenerator.HelperModels;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace EmbeddedEmails.Core.Helpers
{
    public static class HTMLHelpler
    {
        public static string ToHtmlTable<T>(this List<T> listOfClassObjects)
        {
            var ret = string.Empty;

            return listOfClassObjects == null || !listOfClassObjects.Any()
                ? ret
                : "<table>" +
                  listOfClassObjects.First().GetType().GetProperties().Select(p => p.Name).ToList().ToColumnHeaders() +
                  listOfClassObjects.Aggregate(ret, (current, t) => current + t.ToHtmlTableRow()) +
                  "</table>";
        }

        public static string ToColumnHeaders<T>(this List<T> listOfProperties)
        {
            var ret = string.Empty;

            return listOfProperties == null || !listOfProperties.Any()
                ? ret
                : "<tr>" +
                  listOfProperties.Aggregate(ret,
                      (current, propValue) =>
                          current +
                          ("<th style='font-size: 11pt; font-weight: bold; border: 1pt solid black'>" +
                           (Convert.ToString(propValue).Length <= 100
                               ? Convert.ToString(propValue)
                               : Convert.ToString(propValue).Substring(0, 100)) + "..." + "</th>")) +
                  "</tr>";
        }

        public static string ToHtmlTableRow<T>(this T classObject)
        {
            var ret = string.Empty;

            return classObject == null
                ? ret
                : "<tr>" +
                  classObject.GetType()
                      .GetProperties()
                      .Aggregate(ret,
                          (current, prop) =>
                              current + ("<td style='font-size: 11pt; font-weight: normal; border: 1pt solid black'>" +
                                         (Convert.ToString(prop.GetValue(classObject, null)).Length <= 100
                                             ? Convert.ToString(prop.GetValue(classObject, null))
                                             : Convert.ToString(prop.GetValue(classObject, null)).Substring(0, 100) +
                                               "...") +
                                         "</td>")) + "</tr>";
        }

        public static string MakeHtmlTable(DataTable data)
        {
            string[] table = new string[data.Rows.Count + 1];
            table[0] = "<tr><th style='font-size: 11pt; font-weight: bold;border-bottom: solid 1pt;border-right: solid 1pt;padding: 5pt;'>" + string.Join("</th><th style='font-size: 11pt; font-weight: bold;border-bottom: solid 1pt;border-right: solid 1pt;padding: 5pt;'>", (from a_Col in data.Columns.Cast<DataColumn>() select a_Col.ColumnName).ToArray()) + "</th></tr>";
            long counter = 1;
            foreach (DataRow row in data.Rows)
            {
                table[counter] = "<tr><td style='font-size: 11pt; font-weight: normal;border-bottom: solid 1pt;border-right: solid 1pt;padding: 5pt;'>" + String.Join("</td><td style='font-size: 11pt;border-bottom: solid 1pt;border-right: solid 1pt;padding: 5pt;'>", (from o in row.ItemArray select o.ToString()).ToArray()) + "</td></tr>";

                counter += 1;
            }

            return "</br><table style='border-collapse: inherit;border: solid 1pt;text-align: center;'>" + String.Join("", table) + "</table>";
        }

        public static string MakeHtmlTable(ExcelWorksheet data, PivotColumn pivotColumns = null)
        {
            var rowCount = data.Dimension.Rows + 1;
            var colCount = data.Dimension.Columns + 1;
            var pivotValuesCount = pivotColumns == null ? 1 : pivotColumns.ValueColumns.Count();
            string[] table = new string[rowCount + 1];
            var startColumn = 2;
            var startRow = 2;
            var header = "<tr>";
            var pivotIndex = 0;
            for (var i = startColumn; i < colCount; i++)
            {
                var cellData = data.Cells[1, i].Value?.ToString();
                if (!String.IsNullOrWhiteSpace(cellData))
                {
                    break;
                }
                pivotIndex = i;
            }
            if (pivotIndex != 0)
            {
                pivotIndex = pivotIndex + pivotValuesCount - 1;
                for (var col = startColumn; col < colCount; col++)
                {
                    var cellData = data.Cells[1, col].Value?.ToString();
                    if (!String.IsNullOrWhiteSpace(cellData))
                    {
                        header = header + "<th colspan='" + pivotValuesCount + "' style='font-size: 11pt; font-weight: bold;border-bottom: solid 1pt;border-right: solid 1pt;padding: 5pt;'>"
                                    + cellData
                                    + "</th>";
                    }
                    else if (col >= pivotIndex)
                    {
                        continue;
                    }
                    else
                    {
                        header = header + "<th style='font-size: 11pt; font-weight: bold;border-bottom: solid 1pt;border-right: solid 1pt;padding: 5pt;'></th>";
                    }
                }
                header += "</tr>";
            }
            else
            {
                startRow = 1;
            }
            header += "<tr>";
            for (var j = startColumn; j < colCount; j++)
            {
                var cellData = data.Cells[startRow, j].Value?.ToString();
                header = header + "<th style='font-size: 11pt; font-weight: bold;border-bottom: solid 1pt;border-right: solid 1pt;padding: 5pt;'>"
                          + cellData
                          + "</th>";
            }
            header += "</tr>";
            table[0] = header;
            for (int counter = 3; counter < rowCount; counter++)
            {
                var row = data.Cells[counter, startColumn, counter, colCount].Select(c => c.Value?.ToString()).ToList();
                table[counter] = "<tr><td style='font-size: 11pt; font-weight: normal;border-bottom: solid 1pt;border-right: solid 1pt;padding: 5pt;'>"
                    + String.Join("</td><td style='font-size: 11pt;border-bottom: solid 1pt;border-right: solid 1pt;padding: 5pt;'>", row) + "</td></tr>";
            }
            //var dat = "</br><table style='border-collapse: inherit;border: solid 1pt;text-align: center;'>" + String.Join("", table) + "</table>";

            return "</br><table style='border-collapse: inherit;border: solid 1pt;text-align: center;'>" + String.Join("", table) + "</table>";
        }
    }
    public class StringModel
    {
        public string Value { get; set; }
        public string Name { get; set; }
    }
}
