﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.DbStorage.DbModels.TransactionDbModels
{
    public class SchemeSale
    {
        public long Id { get; set; }

        public long SchemeId { get; set; }

        public long? SlabId { set; get; }


        public long AttendanceId { set; get; }

        public int ArticleQty { get; set; }
        public ICollection<SchemeSaleItem> SchemeSaleItems { set; get; }
    }
    public class SchemeSaleItem
    {
        public long Id { get; set; }


        public SchemeSale SchemeSale { get; set; }
        public decimal OrderQty { get; set; }

        public decimal OrderValue { get; set; }

        public decimal FOCValue { get; set; }
        public long ProductId { get; set; }
        public decimal FOCQty { get; set; }
        public double PTRFOC { get; set; }

    }
}
