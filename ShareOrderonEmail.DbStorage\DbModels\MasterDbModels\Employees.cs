﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace ShareOrderonEmail.DbStorage.DbModels.MasterDbModels
{
    [Table("ClientEmployees")]
    public class Employee
    {
        public long Id { get; set; }
        public Guid GUID { get; set; }
        public string Name { get; set; }
        [Column("Deleted")]
        public bool IsDeactive { get; set; }
        public long Company { get; set; }
        public string ClientSideId { get; set; }
        public long? ParentId { get; set; }
        public Employee Parent { get; set; }
        public long? OldTableId { get; set; }
        public string LocalName { get; set; }
        public string ContactNo { get; set; }
        public long? RegionId { get; set; }
        public bool IsFieldAppuser { get; set; }
        public DateTime? DateOfJoining { set; get; }
        public bool IsTrainingUser { get; set; }
        public bool IsOrderBookingDisabled { get; set; }
        public string EmailId { get; set; }
        public long? KRATagId { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? DesignationId { get; set; }
        public string UserProfilePicture { get; set; }
        public long? AreaSalesManagerId { get; set; }
        public string EmployeeAttributeText1 { get; set; }
        public string EmployeeAttributeText2 { get; set; }
        public double? EmployeeAttributeNumber1 { get; set; }
        public double? EmployeeAttributeNumber2 { get; set; }
    }

    [Table("Designations")]
    public class Designations
    {
        public long Id { get; set; }

        [Column("Name")]
        public string Name { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("IsDeactivated")]
        public bool IsDeactivated { get; set; }

        //[Column("ShopTypeIds")]
        //public List<long> ShopTypeIds { get; set; }
    }
}
