﻿using System;
using System.Collections.Generic;
using System.Text;
using ShareOrderonEmail.Core.Repositories.ITransactionRepositories;
using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using System.Linq;
using System.Threading.Tasks;
using Library.StorageWriter;
using iTextSharp.text.pdf;
using ShareOrderonEmail.Core.Models;
using FileGenerator;
using iTextSharp.text;
using FileGenerator.Interfaces;
using Library.EmailService;
using ShareOrderonEmail.Core.Helpers;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System.Text.RegularExpressions;
using System.Globalization;
using Microsoft.Azure.Documents.SystemFunctions;

namespace ShareOrderonEmail.Core.Services
{
    public class ShareOrderonEmailService
    {
        private readonly IAttendanceRepository attendanceRepository;
        private readonly ISaleRepository saleRepository;
        private readonly IDistributorRepository distributorRepository;
        private readonly IEmployeeRepository employeeRepository;
        private readonly ICompanyRepository companyRepository;
        private readonly IBeatRepository beatRepository;
        private readonly ILocationRepository locationRepository;
        private readonly IProductRepository productRepository;
        private readonly INomenclatureSpecifier nomenclatureSpecifier;
        private readonly IDbReceivedPaymentRepository dbReceivedPaymentRepository;
        private readonly ISchemeSaleRepository schemeSaleRepository;
        private readonly EmailSender emailSender;
        private readonly DumpReportBlobWriter blobWriter;
        private readonly IVanRepository vanRepository;


        public ShareOrderonEmailService(IAttendanceRepository attendanceRepository,
            ISaleRepository saleRepository,
            IDistributorRepository distributorRepository,
            IEmployeeRepository employeeRepository,
            ICompanyRepository companyRepository,
            IBeatRepository beatRepository,
            ILocationRepository locationRepository,
            IProductRepository productRepository,
            INomenclatureSpecifier nomenclatureSpecifier,
            IDbReceivedPaymentRepository dbReceivedPaymentRepository,
            ISchemeSaleRepository schemeSaleRepository,
            EmailSender emailSender,
            DumpReportBlobWriter blobWriter,
            IVanRepository vanRepository
            )
        {
            this.attendanceRepository = attendanceRepository;
            this.saleRepository = saleRepository;
            this.distributorRepository = distributorRepository;
            this.employeeRepository = employeeRepository;
            this.companyRepository = companyRepository;
            this.beatRepository = beatRepository;
            this.locationRepository = locationRepository;
            this.productRepository = productRepository;
            this.nomenclatureSpecifier = nomenclatureSpecifier;
            this.dbReceivedPaymentRepository = dbReceivedPaymentRepository;
            this.schemeSaleRepository = schemeSaleRepository; 
            this.emailSender = emailSender;
            this.blobWriter = blobWriter;
            this.vanRepository = vanRepository;
        }

        public async Task ShareOrderDataonEmail(long companyId)
        {
            DateTime fromDate = DateTime.Now.Date;
            DateTime toDate = fromDate.AddDays(1).Date;
            try
            {
                //DateTime fromDate = new DateTime(2025, 5, 29);
                //DateTime toDate = fromDate.AddDays(1);

                var companySettings = new CompanySettings(companyRepository, companyId);
                var indianCulture = new CultureInfo("en-IN");
                var CurrencySymbol = companySettings.CountryCurrencySymbol;
                //var closetoexp = await companyRepository.UsesClosetoExpiry(companyId);
                var closetoexp = companySettings.usesCloseToExpiry;
                var showreturndetails = (await companyRepository.ShowReturnDetailScreen(companyId)).Count > 0 ? true : false;
                var usesRetailerStock = companySettings.IsUsesRetailerStock; //await companyRepository.UsesRetailerStock(companyId);
                var usesIntelligentScheme = companySettings.usesIntelligentSchemes;
                var usesConversionFactor = companySettings.UsesConversionFactor;
                var DiscountType = companySettings.DiscountType;
                var TypeOfTaxCalculation = companySettings.TypeofTaxCalculation;
                var DistributorCashDiscount = companySettings.DistributorCashDiscount;


                var attendancedata = await attendanceRepository.GetDataForCurrentDay(companyId, fromDate, toDate);
                var attendanceIds = attendancedata.Select(a => a.Id).ToList();

                var salesdata = new List<SaleCore>();
                int iterate = 0;
                while (iterate < attendanceIds.Count())
                {
                    var salestemp = await saleRepository.GetSalesData(companyId, attendanceIds.Skip(iterate).Take(3000).ToList());
                    salesdata.AddRange(salestemp);
                    iterate += 3000;
                }
                var salesdict = salesdata.GroupBy(a => a.AttendanceId).ToDictionary(s => s.Key, s => s);


                var attendancedistdict = attendancedata.GroupBy(a => a.EmployeeId).ToDictionary(a => a.Key, a => a.ToList());
                var locationInvoiceMap = attendancedata
                .Where(a => !string.IsNullOrEmpty(a.InvoiceNo))
                .GroupBy(a => a.LocationId)
                .ToDictionary(
                    g => g.Key,
                    g => g.First().InvoiceNo
                );
                var ESMIds = attendancedata.Select(a => a.EmployeeId).Distinct().ToList();
                var ESMDict = await employeeRepository.GetEmployeeDetails(companyId, ESMIds);
                var ESMVanMappingDict = await vanRepository.GetVanMasterData(companyId, ESMIds);

                var BeatIds = attendancedata.Where(a => a.BeatId.HasValue).Select(a => (long)a.BeatId.Value).Distinct().ToList();
                var beatDict = await beatRepository.GetBeatDetails(companyId, BeatIds);

                var ComName = await companyRepository.GetName(companyId);

                var companyData = await companyRepository.GetCompanyDetails(companyId);

                var DistributorIds = attendancedata.Where(a => a.DistributorId.HasValue).Select(a => a.DistributorId.Value).ToList();
                DistributorIds.AddRange(salesdata.Where(s => s.DistributorId.HasValue).Select(s => s.DistributorId.Value).ToList());
                var DistributorDict = await distributorRepository.GetDistributorDetails(companyId, DistributorIds.Distinct().ToList());

                var paymentDict = await dbReceivedPaymentRepository.GetPaymentDetails(attendanceIds);

                var vanOrderDataDict = await employeeRepository.GetVanSalesInvoiceData(attendanceIds);

                var schemeDic = await schemeSaleRepository.GetSchemeFocDetails(attendanceIds);

                var allFOCProductIds = schemeDic.SelectMany(kvp => kvp.Value).Select(s => s.ProductId).Distinct().ToList();

                var getRetailerReturns = await saleRepository.GetRetailerReturns(attendanceIds);

                var locationIds = attendancedata.Select(a => a.LocationId).Distinct().ToList();
                var locData = new List<LocationCore>();
                iterate = 0;
                while (iterate < attendanceIds.Count())
                {
                    var loctemp = await locationRepository.GetlocDetails(companyId, locationIds.Skip(iterate).Take(3000).ToList());
                    locData.AddRange(loctemp);
                    iterate += 3000;
                }
                var locDict = locData.ToDictionary(l => l.Id, l => l);

                var productdict = await productRepository.GetProductDetails(companyId, salesdata.Select(s => s.ProductId).Union(allFOCProductIds).Distinct().ToList());

                var attendancegroupemp = attendancedata.GroupBy(a => a.EmployeeId).ToList();

                var disPdfConfig = await distributorRepository.GetPdfConfigs(companyId);

                var displayNameDictionary = await distributorRepository.GetPdfConfigsForDisplayName(companyId);

                var disPdfConfigKeyDic = new Dictionary<long, string>
                {
                    { 1, "CompanyLogo" },
                    { 2, "CompanyName" }, //d
                    { 3, "CompanyGSTIN" },
                    { 4, "Address" },
                    { 5, "OrderSummary" },
                    { 6, "Date" }, //d                     // Order Date
                    { 7, "DistributorName" }, //d
                    { 8, "DistributorContactNo" }, //d
                    { 9, "DistributorAddress" }, //d
                    { 10, "DistributorGSTIN" },
                    { 11, "RetailerName" },     // Retailer Name or Order Value — adjust as needed
                    { 12, "RetailerNumber" },       // Retailer Number or Quantity — adjust as needed
                    { 13, "RetailerAddress" },
                    { 14, "RetailerGSTIN" },                   // Retailer GSTIN
                    { 15, "EmployeeName" }, //d
                    { 16, "UserNumber" }, //d
                    { 17, "BeatDetails" }, //d
                    { 18, "ProductName" },             // Also: "ModeofPayment"
                    { 19, "ErpCode" },
                    { 20, "HSNCode" },
                    { 21, "Quantity" },                // Also: "StdUnit", "QtyInStdUnit", "QtyInUnit"
                    { 22, "MRP" },
                    { 23, "PTR" },
                    { 24, "FOCQty" },
                    { 25, "Discount" },                // Also: "RetailersDiscount"
                    { 26, "GST" },                     // Also: "GSTIN"
                    { 27, "GSTAmount" },         // GSTAmount 
                    { 28, "CESS" },
                    { 29, "TotalValueInclGST" },                   // Also: "PaymentValue"
                    { 30, "TotalTax" },
                    { 31, "RetailersCGST" }, //d
                    { 32, "RetailersSGST" }, //d
                    { 33, "RetailersIGST" }, //d
                    { 34, "RetailersSchemeDiscount" }, //d
                    { 35, "RetailersNetOrderValue" }, //d
                    { 36, "Remark" }, //d            
                    { 37, "QuantityInSuperUnit" }, //d
                    { 38, "DistributorTaxId" },
                    { 39, "DistributorErpId" },
                    { 40, "DistributorAttributeText1" },
                    { 41, "DistributorAttributeText2" },
                    { 42, "DistributorAttributeText3" },
                    { 43, "DistributorAttributeNumber1" },
                    { 44, "DistributorAttributeNumber2" },
                    { 45, "UserErpID" },
                    { 46, "VehicleNumber" },
                    { 47, "DriverName" },
                    { 48, "DriverNumber" },
                    { 49, "VehicleErpID" },
                    { 50, "EmployeeAttributeText1" },
                    { 51, "EmployeeAttributeText2" },
                    { 52, "EmployeeAttributeNumber1" },
                    { 53, "EmployeeAttributeNumber2" },
                    { 54, "RetailerTaxID" },
                    { 55, "RetailerErpID" },
                    { 56, "AttributeText1" },
                    { 57, "AttributeText2" },
                    { 58, "AttributeText3" },
                    { 59, "AttributeNumber1" },
                    { 60, "AttributeNumber2" },
                    { 61, "AttributeNumber3" },
                    { 62, "ReturnQty" }, //d
                    { 63, "FOCValue" },
                    { 64, "TaxPercent" },
                    { 65, "TaxAmount" },
                    { 66, "PayableAmount" },
                    { 67, "RetailersOrderValue" }, //d                   
                    { 68, "RetailersCESS" }, //d
                    { 69, "TotalFOCBenefit" },
                    { 70, "TotalBenefits" },             // Total Benefits
                    { 71, "InvoiceNumber" }, //d
                    { 72, "SerialNumber" },
                    { 73, "CUInvoiceNumber" },
                    { 74, "QRCode" },
                    { 75, "CompanyTaxID" }
                };

                var attsalesdata = salesdata.GroupJoin(attendancedata, s => s.AttendanceId, a => a.Id, (s, a) => new { sales = s, attn = a.FirstOrDefault() }).Select(s => new AttendanceSalesModel
                {
                    EmployeeId = s.attn.EmployeeId,
                    BeatId = s.attn.BeatId,
                    DeviceTime = s.attn.DeviceTime,
                    RemarksDistributor = s.attn.RemarksDistributor,
                    LocationId = s.attn.LocationId,
                    InvoiceNo = s.attn.InvoiceNo,
                    Id = s.attn.Id,
                    CGST = s.attn.CGST,
                    SGST = s.attn.SGST,
                    IGST = s.attn.IGST,
                    VAT = s.attn.VAT,
                    Excise = s.attn.Excise,
                    //Sales

                    SaleValue = s.sales.SaleValue,
                    PTR = s.sales.PTR,
                    MRP = s.sales.MRP,
                    ProductId = s.sales.ProductId,
                    StockQuantity = s.sales.StockQuantity,
                    ReturnQuantity = s.sales.ReturnQuantity,
                    NearExpiryStockQuantity = s.sales.NearExpiryStockQuantity,
                    Discount = s.sales.Discount,
                    DistributorCashDiscount = s.sales.DistributorCashDiscount,
                    SchemeCashDiscount = s.sales.SchemeCashDiscount,
                    DistributorId = s.sales.DistributorId.HasValue ? s.sales.DistributorId : s.attn.DistributorId,
                    StandardUnitConversionFactor = s.sales.StandardUnitConversionFactor,
                    SuperUnitConversionFactor = s.sales.SuperUnitConversionFactor ?? 0
                }).ToList();
                var xx = attsalesdata.GroupBy(a => a.EmployeeId);
                foreach (var xxx in xx)
                {
                    var yy = xxx.GroupBy(a => a.DistributorId);
                    foreach (var yyy in yy)
                    {
                        var resultdata = new List<LocationSummaryDataModel>();
                        resultdata = yyy.GroupBy(a => a.Id).Select(a => new LocationSummaryDataModel
                        {
                            EmployeeName = ESMDict.ContainsKey(a.FirstOrDefault().EmployeeId) ? ESMDict[a.FirstOrDefault().EmployeeId].Name : "",
                            EmployeeContactNo = ESMDict.ContainsKey(a.FirstOrDefault().EmployeeId) ? ESMDict[a.FirstOrDefault().EmployeeId].ContactNo : "",
                            EmployeeErpId = ESMDict.ContainsKey(a.FirstOrDefault().EmployeeId) ? ESMDict[a.FirstOrDefault().EmployeeId].EmployeeErpId : "",
                            VanName = ESMVanMappingDict.ContainsKey(a.FirstOrDefault().EmployeeId) ? ESMVanMappingDict[a.FirstOrDefault().EmployeeId].VanName : "",
                            VanRegistrationNumber = ESMVanMappingDict.ContainsKey(a.FirstOrDefault().EmployeeId) ? ESMVanMappingDict[a.FirstOrDefault().EmployeeId].VanRegistrationNumber : "",
                            Designation = ESMDict.ContainsKey(a.FirstOrDefault().EmployeeId) ? ESMDict[a.FirstOrDefault().EmployeeId].Designation : "",
                            EmployeeAttributeNumber1 = ESMDict.ContainsKey(a.FirstOrDefault().EmployeeId) ? ESMDict[a.FirstOrDefault().EmployeeId].EmployeeAttributeNumber1 : null,
                            EmployeeAttributeNumber2 = ESMDict.ContainsKey(a.FirstOrDefault().EmployeeId) ? ESMDict[a.FirstOrDefault().EmployeeId].EmployeeAttributeNumber2 : null,
                            EmployeeAttributeText1 = ESMDict.ContainsKey(a.FirstOrDefault().EmployeeId) ? ESMDict[a.FirstOrDefault().EmployeeId].EmployeeAttributeText1 : null,
                            EmployeeAttributeText2 = ESMDict.ContainsKey(a.FirstOrDefault().EmployeeId) ? ESMDict[a.FirstOrDefault().EmployeeId].EmployeeAttributeText2 : null,
                            DistributorName = DistributorDict.ContainsKey(a.FirstOrDefault().DistributorId.Value) ? DistributorDict[a.FirstOrDefault().DistributorId.Value].Name : null,
                            DistributorContactNo = DistributorDict.ContainsKey(a.FirstOrDefault().DistributorId.Value) ? DistributorDict[a.FirstOrDefault().DistributorId.Value].ContactNo : null,
                            DistributorAddress = DistributorDict.ContainsKey(a.FirstOrDefault().DistributorId.Value) ? DistributorDict[a.FirstOrDefault().DistributorId.Value].Address : null,
                            DistributorGSTIN = DistributorDict.ContainsKey(a.FirstOrDefault().DistributorId.Value) ? DistributorDict[a.FirstOrDefault().DistributorId.Value].GSTIN : null,
                            DistributorErpId = DistributorDict.ContainsKey(a.FirstOrDefault().DistributorId.Value) ? DistributorDict[a.FirstOrDefault().DistributorId.Value].ClientSideId : null,
                            DistributorAttributeNumber1 = DistributorDict.ContainsKey(a.FirstOrDefault().DistributorId.Value) ? DistributorDict[a.FirstOrDefault().DistributorId.Value].Distributor_AttributeNumber1 : null,
                            DistributorAttributeNumber2 = DistributorDict.ContainsKey(a.FirstOrDefault().DistributorId.Value) ? DistributorDict[a.FirstOrDefault().DistributorId.Value].Distributor_AttributeNumber2 : null,
                            DistributorAttributeText1 = DistributorDict.ContainsKey(a.FirstOrDefault().DistributorId.Value) ? DistributorDict[a.FirstOrDefault().DistributorId.Value].Distributor_AttributeText1 : null,
                            DistributorAttributeText2 = DistributorDict.ContainsKey(a.FirstOrDefault().DistributorId.Value) ? DistributorDict[a.FirstOrDefault().DistributorId.Value].Distributor_AttributeText2 : null,
                            DistributorAttributeText3 = DistributorDict.ContainsKey(a.FirstOrDefault().DistributorId.Value) ? DistributorDict[a.FirstOrDefault().DistributorId.Value].Distributor_AttributeText3 : null,
                            CompanyName = ComName,
                            CompanyAddress = companyData.Address,
                            CompanyGSTIN = companyData.GSTIN,
                            BeatName = a.FirstOrDefault().BeatId.HasValue && beatDict.ContainsKey((long)a.FirstOrDefault().BeatId.Value) ? beatDict[(long)a.FirstOrDefault().BeatId.Value] : "",
                            Date = a.FirstOrDefault().DeviceTime.ToString("dd/MM/yyyy"),
                            InvoiceNumber = locationInvoiceMap.ContainsKey(a.FirstOrDefault().LocationId) ? locationInvoiceMap[a.FirstOrDefault().LocationId] : null,
                            OrderTime = a.FirstOrDefault().DeviceTime.TimeOfDay.ToString(),
                            LocationName = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].ShopName : null,
                            LocationAddress = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].Address : null,
                            LocationNumber = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].OwnersNo : null,
                            GSTIN = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].GSTIN : null,
                            PlaceofDelivery = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].PlaceOfDelivery : null,
                            RetailerErpId = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].ErpId : null,
                            AttributeNumber1 = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].AttributeNumber1 : null,
                            AttributeNumber2 = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].AttributeNumber2 : null,
                            AttributeNumber3 = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].AttributeNumber3 : null,
                            AttributeText1 = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].AttributeText1 : null,
                            AttributeText2 = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].AttributeText2 : null,
                            AttributeText3 = locDict.ContainsKey(a.FirstOrDefault().LocationId) ? locDict[a.FirstOrDefault().LocationId].AttributeText3 : null,
                            PlaceofSupply = DistributorDict.ContainsKey(a.FirstOrDefault().DistributorId.Value) ? DistributorDict[a.FirstOrDefault().DistributorId.Value].PlaceOfSupply : null,
                            Remark = a.FirstOrDefault().RemarksDistributor,
                            //DataForSec = a.Select(s => new SaleDataDetails
                            //{
                            //    ProductName = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId].Name : null,
                            //    ErpCode = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId].ErpCode.ToString() : null,
                            //    UnitPrice = s.PTR.ToString(),
                            //    Quantity = s.SaleValue,
                            //    //s.SaleValue.ToString() + " " + (productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId].Unit : "Place"),
                            //    Value = (s.SaleValue * s.PTR),
                            //    PTR = s.PTR,
                            //    MRP = s.MRP,
                            //    FOCQty = schemeDic.ContainsKey(s.Id) ? schemeDic[s.Id].Where(ss => ss.ProductId == s.ProductId && ss.FocQty > 0).Sum(ss => ss.FocQty) : 0,
                            //    FOCValue = schemeDic.ContainsKey(s.Id) ? schemeDic[s.Id].Where(ss => ss.ProductId == s.ProductId && ss.FocQty > 0).Sum(ss => ss.FocValue) : 0,
                            //    Discount = s.Discount,
                            //    StockQty = s.StockQuantity != null ? s.StockQuantity.Value : 0,
                            //    //(s.StockQuantity != null? s.StockQuantity.ToString() :"0") + " " + (productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId].Unit : "Place"),
                            //    ReturnQty = getRetailerReturns.ContainsKey(s.Id) ? getRetailerReturns[s.Id].Where(aa => aa.ProductId == s.ProductId).Select(ss => ss.ReturnQuantity).FirstOrDefault() : 0,
                            //    //(getRetailerReturns.ContainsKey(a.Id) ? getRetailerReturns[a.Id].Where(aa=> aa.ProductId == s.ProductId).Select(ss=> ss.ReturnQuantity).FirstOrDefault().ToString() : "0") + " " + (productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId].Unit : "Place"),
                            //    Closetoexp = s.NearExpiryStockQuantity != null ? s.NearExpiryStockQuantity.Value : 0,
                            //    Unit = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId].Unit : "Place",
                            //    StdUnit = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId].StandardUnit : "",
                            //    SuperUnit = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId].SuperUnit : "",
                            //    QtyInStdUnit = (long)(s.SaleValue / s.StandardUnitConversionFactor),
                            //    QtyInUnit = (long)(s.SaleValue % s.StandardUnitConversionFactor),
                            //    QtyInSuperUnit = s.SuperUnitConversionFactor != 0 ? (decimal)(s.SaleValue / s.SuperUnitConversionFactor) : 0,
                            //    GST = (decimal)(s.SaleValue * s.PTR) > 0
                            //            ? (((s.SGST.GetValueOrDefault() + s.CGST.GetValueOrDefault() + s.IGST.GetValueOrDefault()) /
                            //               ((decimal)(s.SaleValue * s.PTR) - ((decimal?)s.Discount ?? 0) - ((decimal?)s.DistributorCashDiscount ?? 0) - ((decimal?)s.SchemeCashDiscount ?? 0))) * 100)
                            //               .ToString("F2") : null,
                            //    HSNCode = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId].HSNCode.ToString() : null,
                            //    CESS = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId].CESS : null,
                            //    //(s.NearExpiryStockQuantity != null ? s.NearExpiryStockQuantity.ToString() : "0") + " " + (productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId].Unit : "Place"),
                            //}).ToList(),

                            DataForSec = a.SelectMany(s =>
                            {
                                var list = new List<SaleDataDetails>();
                                var tempMainItemList = new HashSet<long>();
                                var product = salesdict.ContainsKey(s.Id) ? salesdict[s.Id].Where(pro => pro.ProductId == s.ProductId).FirstOrDefault() : null;
                                var mainItem = new SaleDataDetails
                                {
                                    ProductName = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId]?.Name : null,
                                    ErpCode = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId]?.ErpCode?.ToString() : null,
                                    UnitPrice = s.PTR.ToString("N2", indianCulture),
                                    Quantity = Math.Round(s.SaleValue, 2, MidpointRounding.AwayFromZero),
                                    Value = Math.Round(s.SaleValue * s.PTR, 2, MidpointRounding.AwayFromZero),
                                    PTR = Math.Round(s.PTR, 2, MidpointRounding.AwayFromZero),
                                    FOCPTR = schemeDic != null && schemeDic.ContainsKey(s.Id)
                                        ? schemeDic[s.Id]?.Where(sc => sc.ProductId == s.ProductId).Sum(sc => sc?.FocPTR ?? 0)
                                        : 0,

                                                                    MRP = s.MRP.HasValue
                                        ? (double?)Math.Round((decimal)s.MRP.Value, 2, MidpointRounding.AwayFromZero)
                                        : null,

                                    FOCQty = schemeDic != null && schemeDic.ContainsKey(s.Id)
                                        ? schemeDic[s.Id]?.Where(sc => sc.ProductId == s.ProductId).Sum(sc => sc?.FocQty ?? 0) ?? 0
                                        : 0,

                                    FOCValue = schemeDic != null && schemeDic.ContainsKey(s.Id)
                                        ? (decimal)(schemeDic[s.Id]
                                            ?.Where(sc => sc.ProductId == s.ProductId)
                                            .Sum(sc => sc?.FocPTR ?? 0) ?? 0)
                                        : 0,
                                    Discount = Math.Round((s.Discount) * s.PTR, 2, MidpointRounding.AwayFromZero),

                                                                    StockQty = Math.Round(s.StockQuantity ?? 0.00, 2, MidpointRounding.AwayFromZero),

                                                                    ReturnQty = Math.Round(
                                        getRetailerReturns != null && getRetailerReturns.ContainsKey(s.Id)
                                            ? getRetailerReturns[s.Id]?.Where(aa => aa.ProductId == s.ProductId).Select(ss => ss?.ReturnQuantity ?? 0).FirstOrDefault()
                                            ?? 0.00
                                            : 0.00,
                                        2, MidpointRounding.AwayFromZero),

                                    Closetoexp = Math.Round(s.NearExpiryStockQuantity ?? 0.00, 2, MidpointRounding.AwayFromZero),

                                    Unit = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId]?.Unit ?? "Place" : "Place",
                                    StdUnit = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId]?.StandardUnit ?? "" : "",
                                    SuperUnit = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId]?.SuperUnit ?? "" : "",
                                    QtyInStdUnit = s.StandardUnitConversionFactor != 0
                                    ? (long)(s.SaleValue / s.StandardUnitConversionFactor)
                                    : 0,

                                                                QtyInUnit = s.StandardUnitConversionFactor != 0
                                    ? (long)(s.SaleValue % s.StandardUnitConversionFactor)
                                    : 0,


                                                                QtyInSuperUnit = s.SuperUnitConversionFactor != 0
                                    ? (decimal)(s.SaleValue / s.SuperUnitConversionFactor)
                                    : 0,

                                    //GST = productdict.ContainsKey(s.ProductId)
                                    //? ((productdict[s.ProductId]?.IGST ?? 0) + (productdict[s.ProductId]?.CGST ?? 0) + (productdict[s.ProductId]?.SGST ?? 0)).ToString("N2", indianCulture)
                                    //: null,
                                    GST = productdict.ContainsKey(s.ProductId)
                                    ? ((productdict[s.ProductId]?.CGST ?? 0) + (productdict[s.ProductId]?.SGST ?? 0)).ToString("N2", indianCulture)
                                    : null,
                                    TaxPercent = productdict.ContainsKey(s.ProductId)
                                    ? (productdict[s.ProductId]?.VAT ?? 0).ToString("N2", indianCulture)
                                    : null,

                                    HSNCode = productdict.ContainsKey(s.ProductId) ? productdict[s.ProductId]?.HSNCode?.ToString() : null,

                                    CESS = productdict.ContainsKey(s.ProductId)
                                    ? (decimal?)Math.Round(
                                        ((decimal)(productdict[s.ProductId]?.CESS ?? 0) / 100) * ((decimal)s.PTR * (decimal)s.SaleValue),
                                        2,
                                        MidpointRounding.AwayFromZero)
                                    : null,

                                    //GSTAmount = s.CGST.GetValueOrDefault() + s.IGST.GetValueOrDefault() + s.SGST.GetValueOrDefault()
                                    GSTAmount = ((product?.SGST ?? 0.00M) + (product?.CGST ?? 0.00M)+ (product?.IGST ?? 0.00M)),
                                    TaxAmount = (product?.VAT ?? 0.00M),
                                    igst = (product?.IGST ?? 0.00M),
                                    cgst = (product?.CGST ?? 0.00M),
                                    sgst = (product?.SGST ?? 0.00M)
                                };

                                if(mainItem.Quantity > 0)
                                {
                                    list.Add(mainItem);
                                    //Add Additional FOC items (for different ProductId)
                                    if (schemeDic.ContainsKey(s.Id))
                                    {
                                        var productIdsInDataForSec = new HashSet<long>(a.Select(x => x.ProductId));

                                        var otherFOCs = schemeDic[s.Id].Where(sc => sc.FocQty > 0 && !productIdsInDataForSec.Contains(sc.ProductId));

                                        //var otherFOCs = schemeDic[s.Id].Where(sc => sc.FocQty > 0);
                                        foreach (var foc in otherFOCs)
                                        {
                                            list.Add(new SaleDataDetails
                                            {
                                                ProductName = productdict.ContainsKey(foc.ProductId) ? productdict[foc.ProductId].Name + " (FOC)" : "Unknown FOC Product",
                                                ErpCode = productdict.ContainsKey(foc.ProductId) ? productdict[foc.ProductId].ErpCode?.ToString() : null,
                                                FOCQty = foc.FocQty,
                                                FOCValue = (decimal)foc.FocPTR,
                                                FOCPTR = foc.FocPTR,
                                                Quantity = 0,
                                                Value = 0,
                                                PTR = 0.00,
                                                MRP = productdict.ContainsKey(foc.ProductId) ? (double?)productdict[foc.ProductId].MRP : null,
                                                Unit = productdict.ContainsKey(foc.ProductId) ? productdict[foc.ProductId].Unit : "Place",
                                                StdUnit = productdict.ContainsKey(foc.ProductId) ? productdict[foc.ProductId].StandardUnit : "",
                                                SuperUnit = productdict.ContainsKey(foc.ProductId) ? productdict[foc.ProductId].SuperUnit : "",
                                                HSNCode = productdict.ContainsKey(foc.ProductId) ? productdict[foc.ProductId].HSNCode.ToString() : null,
                                                CESS = 0.00m,
                                                GST = "0.00",
                                                TaxPercent = "0.00",
                                                Discount = 0,
                                                StockQty = 0,
                                                ReturnQty = 0,
                                                Closetoexp = 0,
                                                QtyInStdUnit = 0,
                                                QtyInUnit = 0,
                                                QtyInSuperUnit = 0,
                                                GSTAmount = 0,
                                                TaxAmount = 0,
                                                UnitPrice = "0.00",
                                            });
                                        }
                                    }
                                }

                                return list;
                            }).ToList(),

                            RetailersQuantity = a.Sum(s => s.SaleValue),
                            RetailersOrderValue = a.Sum(s => s.SaleValue * s.PTR),
                            RetailerOrderTax = a.Select(s => s.VAT).FirstOrDefault(),
                            RetailerStockQty = a.Sum(s => s.StockQuantity),
                            RetailerReturnQty = getRetailerReturns.ContainsKey(a.FirstOrDefault().Id) ? getRetailerReturns[a.FirstOrDefault().Id].Select(s => s.ReturnQuantity).Sum() : (double?)null,
                            RetailerClosetoExp = a.Sum(s => s.NearExpiryStockQuantity),
                            RetailersCashDiscount = a.Sum(s => s.DistributorCashDiscount), //salesDict.ContainsKey(a.Id) ? salesDict[a.Id].Select(s => s.DistributorCashDiscount).Sum() > 0 ? salesDict[a.Id].Select(s => s.DistributorCashDiscount).Sum() : (double?)null : (double?)null,
                            RetailersDiscount = a.Sum(s => s.Discount), //salesDict.ContainsKey(a.Id) ? salesDict[a.Id].Select(s => s.Discount).Sum() > 0 ? salesDict[a.Id].Select(s => s.Discount).Sum() : (double?)null : (double?)null,
                            RetailersCGST = a.Select(s => s.CGST).FirstOrDefault(),
                            RetailersSGST = a.Select(s => s.SGST).FirstOrDefault(),
                            RetailersIGST = a.Select(s => s.IGST).FirstOrDefault(),
                            RetailersSchemeDiscount = a.Where(s => s.SaleValue > 0).Sum(s => s.SchemeCashDiscount), //a.DiscountInAmount - (salesDict.ContainsKey(a.Id) ? salesDict[a.Id].Select(s => s.Discount).Sum() : 0) > 0 ? a.DiscountInAmount -
                                                                                                                    //(salesDict.ContainsKey(a.Id) ? salesDict[a.Id].Select(s => s.Discount).Sum() > 0 ? salesDict[a.Id].Select(s => s.Discount).Sum() : 0 : 0) : (double?)null,
                            FOCBenefit  = a.Where(s=>s.StockQuantity > 0).Sum(s =>(schemeDic.ContainsKey(s.Id) ? schemeDic[s.Id].Sum(sc => sc.FocQty * (decimal)sc.FocPTR): 0)),
                            RetailersNetOrderValue = a.Sum(s => (decimal)(s.SaleValue * s.PTR - s.Discount - (s.DistributorCashDiscount ?? 0))) - a.Where(s => s.SaleValue > 0).Sum(s => s.SchemeCashDiscount),// - a.Sum(s => s.IGST + s.SGST + s.CGST),//((decimal)a.OrderInRevenue - a.Discount - (decimal)a.DiscountInAmount -
                                                                                                                                                                                                               //(salesDict.ContainsKey(a.Id) ? (decimal)salesDict[a.Id].Select(s => s.DistributorCashDiscount).Sum() : 0) + (decimal)(a.SGST ?? 0 + a.IGST ?? 0 + a.SGST ?? 0)),
                            PaymentValue = paymentDict.ContainsKey(a.FirstOrDefault().Id) ? paymentDict[a.FirstOrDefault().Id].Amount > 0 ? paymentDict[a.FirstOrDefault().Id].Amount : null : null,
                            ModeofPayment = paymentDict.ContainsKey(a.FirstOrDefault().Id) ? paymentDict[a.FirstOrDefault().Id].ModeofPayment : null

                        }).ToList();
                        foreach (var x in resultdata)
                        {
                            x.DataForSec = x.DataForSec.OrderBy(a => a.ProductName).ToList();
                            int i = 1;
                            foreach (var prod in x.DataForSec)
                            {
                                prod.ProductName = $"{i++}. {prod.ProductName}";
                            }
                        }
                        var agg = new LocationSummaryAggregate
                        {
                            TotalQuantity = resultdata.Select(a => a.RetailersQuantity).Sum().ToString(),
                            TotalOrderValue = resultdata.Select(a => a.RetailersOrderValue).Sum().ToString(),
                            TotalTax = resultdata.Select(a => a.RetailerOrderTax).Sum().ToString(),
                            TotalStockQty = resultdata.Select(a => a.RetailerStockQty).Sum().ToString(),
                            TotalReturnQty = resultdata.Select(a => a.RetailerReturnQty).Sum().ToString(),
                            TotalClosetoExp = resultdata.Select(a => a.RetailerClosetoExp).Sum().ToString(),
                            TotalCashDiscount = resultdata.Select(a => a.RetailersCashDiscount).Sum() > 0 ? resultdata.Select(a => a.RetailersCashDiscount).Sum().ToString() : null,
                            TotalDiscount = ((double)(resultdata.Sum(a => (decimal?)a.RetailersDiscount ?? 0) +
                                                         resultdata.Sum(a => (decimal?)a.RetailersSchemeDiscount ?? 0) +
                                                         resultdata.Sum(a => (decimal?)a.RetailersCashDiscount ?? 0))
                                              ).ToString(),

                            TotalIGST = resultdata.Select(a => a.RetailersIGST).Sum().ToString(),
                            TotalCGST = resultdata.Select(a => a.RetailersCGST).Sum().ToString(),
                            TotalSGST = resultdata.Select(a => a.RetailersSGST).Sum().ToString(),
                            TotalSchemeDiscount = resultdata.Select(a => a.RetailersSchemeDiscount).Sum() > 0 ? resultdata.Select(a => a.RetailersSchemeDiscount).Sum().ToString() : null,
                            TotalCESS = resultdata.Select(a => a.DataForSec.Select(s => s.CESS ?? 0).Sum()).Sum() > 0 ? resultdata.Select(a => a.DataForSec.Select(s => s.CESS ?? 0).Sum()).Sum().ToString() : null,

                            TotalNetOrderValue = resultdata.Select(a => a.RetailersNetOrderValue).Sum().ToString(),
                            TotalPaymentCollected = resultdata.Select(a => a.PaymentValue).Sum() > 0 ? resultdata.Select(a => a.PaymentValue).Sum().ToString() : null,
                            TotalFocQty = resultdata.Select(a => a.DataForSec.Select(s => s.FOCQty).Sum()).Sum() > 0 ? resultdata.Select(a => a.DataForSec.Select(s => s.FOCQty).Sum()).Sum().ToString() : null,
                            TotalFocValue = resultdata.Select(a => a.DataForSec.Select(s => s.FOCPTR).Sum()).Sum() > 0 ? resultdata.Select(a => a.DataForSec.Select(s => s.FOCPTR).Sum()).Sum().ToString() : null,
                            TotalFOCBenefit = resultdata.Select(a => a.FOCBenefit).Sum().ToString(),

                        };
                        var result = GetPdfModel(resultdata, CurrencySymbol, usesConversionFactor);
                        long dist = yyy.FirstOrDefault().DistributorId.HasValue ? yyy.FirstOrDefault().DistributorId.Value : 0;
                        resultdata.Clear();
                        if (result.Count > 0)
                        {
                            var ESMName = ESMDict.ContainsKey(yyy.FirstOrDefault().EmployeeId) ? ESMDict[yyy.FirstOrDefault().EmployeeId].Name : "FA";
                            var DistName = DistributorDict.ContainsKey(dist) ? DistributorDict[dist].Name : "FA";
                            var filename = $"Orders_{DistName.Replace("\t", "")}_{ESMName.Replace("\t", "")}_{toDate.ToShortDateString()}.pdf";
                            //"Testing16.pdf";//"DSR_" + employee.Name.Replace(" ", "_") + "_" + employeeDSR.Date.ToShortDateString() + "_" + (employeeDSR.DistributorId.HasValue ? employeeDSR.DistributorId.Value.ToString() : "") + ".pdf";
                            var stream = await blobWriter.GetWriteStream($"DSR/{filename}", "application/pdf");
                            var publicurl = blobWriter.GetPublicPath($"DSR/{filename}");
                            //var dataForSecTable = data.Where(x => x.OutletQty > 0);
                            var dataForSecTable = new List<SaleDetails>();
                            var sheetname = $"Daily Sales Report_{ESMName.Replace("\t", "")}";
                            PDFGenerator pDFGenerator = new PDFGenerator(nomenclatureSpecifier.GetSpecifierForCompany(companyId));
                            var columnsToDelete = new List<string>();
                            //if (resultdata.Select(s => s.DataForSec.Where(a => a.FOCQty != 0).Count()).Sum() == 0)
                            //{
                            //    columnsToDelete.Add("FOCQty");
                            //}
                            //if (resultdata.Select(s => s.DataForSec.Where(a => a.FOCValue != 0).Count()).Sum() == 0)
                            //{
                            //    columnsToDelete.Add("FOCValue");
                            //}
                            //if (DiscountType == "Default" || resultdata.Select(s => s.DataForSec.Where(a => a.Discount != 0).Count()).Sum() == 0)
                            //{
                            //    columnsToDelete.Add("Discount");
                            //    columnsToDelete.Add("RetailersDiscount");
                            //}
                            //if (TypeOfTaxCalculation == "Default" || resultdata.Select(a => a.RetailerOrderTax).Sum() == 0)
                            //{
                            //    columnsToDelete.Add("RetailersOrderTax");
                            //    columnsToDelete.Add("RetailersCGST");
                            //    columnsToDelete.Add("RetailersSGST");
                            //    columnsToDelete.Add("TotalTax");
                            //}
                            //if (!DistributorCashDiscount || resultdata.Select(a => a.RetailersCashDiscount).Sum() == 0)
                            //{
                            //    columnsToDelete.Add("RetailersCashDiscount");
                            //    columnsToDelete.Add("TotalCashDiscount");
                            //}
                            //if (!usesRetailerStock || !closetoexp || resultdata.Select(a => a.RetailerClosetoExp).Sum() == 0)
                            //{
                            //    columnsToDelete.Add("RetailerClosetoExp");
                            //    columnsToDelete.Add("Closetoexp");
                            //    columnsToDelete.Add("TotalClosetoExp");
                            //}
                            //if (!usesRetailerStock || !showreturndetails || resultdata.Select(a => a.RetailerReturnQty).Sum() == 0)
                            //{
                            //    columnsToDelete.Add("RetailerReturnQty");
                            //    columnsToDelete.Add("ReturnQty");
                            //    columnsToDelete.Add("TotalReturnQty");
                            //}
                            //if (!usesRetailerStock || !closetoexp || resultdata.Select(a => a.RetailerStockQty).Sum() == 0)
                            //{
                            //    columnsToDelete.Add("RetailerStockQty");
                            //    columnsToDelete.Add("StockQty");
                            //    columnsToDelete.Add("TotalStockQty");
                            //}

                            // Add all the keys which we do not want to be visible
                            disPdfConfig.ForEach(item =>
                            {
                                if (disPdfConfigKeyDic.ContainsKey(item))
                                {
                                    columnsToDelete.Add(disPdfConfigKeyDic[item]);
                                }
                            });

                            var consolidatedData = result.SelectMany(l => l.DataForSec).GroupBy(s => new { s.ErpCode, s.ProductName }).Select(g => new SaleDetails
                            {
                                ProductName = g.Key.ProductName,
                                ErpCode = g.Key.ErpCode,
                                MRP = g.Select(s => s.MRP).FirstOrDefault(),
                                PTR = g.Select(s => s.PTR).FirstOrDefault(),
                                UnitPrice = g.Select(s => s.UnitPrice).FirstOrDefault(),
                                FOCQty = AggregateQuantity(g.Select(s => s.FOCQty)),
                                FOCValue = CurrencySymbol + g.Sum(s => ExtractDecimalValue(s.FOCValue)).ToString("N2", indianCulture),
                                Quantity = AggregateQuantityForQuantityString(g.Select(s => s.Quantity)),
                                Value = CurrencySymbol + g.Sum(s => ExtractDecimalValue(s.Value)).ToString("N2", indianCulture),
                                CESS = CurrencySymbol + g.Sum(s => ExtractDecimalValue(s.CESS)).ToString("N2", indianCulture),
                                GST = g.Select(s => s.GST).FirstOrDefault(),
                                GSTAmount = CurrencySymbol + g.Sum(s => ExtractDecimalValue(s.GSTAmount)).ToString("N2", indianCulture),
                                TaxPercent = g.Select(s => s.TaxPercent).FirstOrDefault(),
                                TaxAmount = CurrencySymbol + SumDecimalFields(g.Select(s => s.TaxAmount)).ToString("N2", indianCulture),
                                HSNCode = g.Select(s => s.HSNCode).FirstOrDefault(),
                                Unit = g.Select(s => s.Unit).FirstOrDefault(),
                                QuantityInSuperUnit = AggregateQuantityForQuantityString(g.Select(s => s.QuantityInSuperUnit)),
                                Discount = CurrencySymbol + g.Sum(s => ExtractDecimalValue(s.Discount)).ToString("N2", indianCulture),
                                StockQty = AggregateQuantity(g.Select(s => s.StockQty)),
                                ReturnQty = AggregateQuantity(g.Select(s => s.ReturnQty)),
                                Closetoexp = AggregateQuantity(g.Select(s => s.Closetoexp)),
                            }).OrderBy(s => s.ProductName == "TOTAL" ? 1 : 0).ToList();

                            var consolidatedModel = new LocationSummaryModel();
                            consolidatedModel.RetailersCESS = agg.TotalCESS;
                            consolidatedModel.DataForSec = consolidatedData;
                            consolidatedModel.RetailersIGST = agg.TotalIGST;
                            consolidatedModel.RetailersCGST = agg.TotalCGST;
                            consolidatedModel.RetailersSGST = agg.TotalSGST;
                            consolidatedModel.Date = result.FirstOrDefault().Date;
                            consolidatedModel.RetailersSchemeDiscount = agg.TotalSchemeDiscount;
                            consolidatedModel.TotalFOCBenefit = agg.TotalFOCBenefit;
                            consolidatedModel.RetailersOrderValue = agg.TotalOrderValue;
                            consolidatedModel.TotalFocValue = agg.TotalFocValue;
                            consolidatedModel.TotalTax = agg.TotalTax;
                            var consolidatedList = new List<LocationSummaryModel> { consolidatedModel };
                            // OR (more readable)
                            var jsonconsolidatedList = Newtonsoft.Json.JsonConvert.SerializeObject(consolidatedList, Newtonsoft.Json.Formatting.Indented);
                            var jsonconsolidatedData = Newtonsoft.Json.JsonConvert.SerializeObject(consolidatedData, Newtonsoft.Json.Formatting.Indented);
                            ////List<PdfPTable> consolidatedTable = pDFGenerator.CreateFlatDataTable(consolidatedData, companyId, null, new List<string>(), 2, columnsToDelete);
                             //pDFGenerator.MakeMultipleDataPDFV2(companyId, stream, "Consolidated Data", consolidatedList, consolidatedData, 2, null, null, null, columnsToDelete, null);

                            //List<PdfPTable> totalTable = pDFGenerator.CreateFlatDataTable(agg, companyId, null, new List<string>(), 2, columnsToDelete);
                            pDFGenerator.MakeMultipleDataPDFV2(companyId, CurrencySymbol, stream, sheetname, result, dataForSecTable, consolidatedList, consolidatedData, 2, displayNameDictionary, null, null, null, columnsToDelete);
                            stream.Close();        
                            if (DistributorDict.ContainsKey(dist) && DistributorDict[dist].EmailId != null)
                            {
                                var emailcc = new List<string>();
                                if (ESMDict.ContainsKey(yyy.FirstOrDefault().EmployeeId) && !string.IsNullOrWhiteSpace(ESMDict[yyy.FirstOrDefault().EmployeeId].EmailId))
                                {
                                    emailcc.Add(ESMDict[yyy.FirstOrDefault().EmployeeId].EmailId);
                                }
                                if (ESMDict.ContainsKey(yyy.FirstOrDefault().EmployeeId) && !string.IsNullOrWhiteSpace(ESMDict[yyy.FirstOrDefault().EmployeeId].ParentEmailId))
                                {
                                    emailcc.Add(ESMDict[yyy.FirstOrDefault().EmployeeId].ParentEmailId);
                                }
                                
                                var emailMessage = new EmailMessage
                                {
                                    FromEmail = "<EMAIL>",
                                    FromName = "FA-Support",
                                    IsHTML = true,
                                    Message = EmailTemplates.ShareOrderEmail(publicurl, DistributorDict.ContainsKey(dist) ? DistributorDict[dist].Name : null),
                                    Subject = $"{ESMName}|{ComName}",
                                    To = DistributorDict[dist].EmailId,
                                    Cc = string.Join(",", emailcc),
                                    // Bcc = "<EMAIL>"
                                };
                                await emailSender.Send(emailMessage);
                            }

                            result.Clear();

                        }


                    }
                }
            }
            catch (Exception ex)
            {

                throw;
            }
        }
        public List<LocationSummaryModel> GetPdfModel(List<LocationSummaryDataModel> resultdata, string CurrencySymbol, bool UsesConversion)
        {
            
                {// fetch from company settings
                    var indianCulture = new CultureInfo("en-IN");
                    try {
                    var abc = resultdata.Select(r => new LocationSummaryModel
                    {
                        EmployeeName = r.EmployeeName,
                        EmployeeErpId = r.EmployeeErpId,
                        EmployeeContactNo = r.EmployeeContactNo,
                        Designation = r.Designation,
                        VanName = r.VanName,
                        VanRegistrationNumber = r.VanRegistrationNumber,
                        DistributorName = r.DistributorName,
                        DistributorContactNo = r.DistributorContactNo,
                        DistributorAddress = r.DistributorAddress,
                        DistributorERPId = r.DistributorErpId,
                        DistributorGSTIN = r.DistributorGSTIN,
                        EmployeeAttributeNumber1 = r.EmployeeAttributeNumber1?.ToString(),
                        EmployeeAttributeNumber2 = r.EmployeeAttributeNumber2?.ToString(),
                        EmployeeAttributeText1 = r.EmployeeAttributeText1,
                        EmployeeAttributeText2 = r.EmployeeAttributeText2,
                        DistributorAttributeNumber1 = r.DistributorAttributeNumber1?.ToString(),
                        DistributorAttributeNumber2 = r.DistributorAttributeNumber2?.ToString(),
                        DistributorAttributeText1 = r.DistributorAttributeText1,
                        DistributorAttributeText2 = r.DistributorAttributeText2,
                        DistributorAttributeText3 = r.DistributorAttributeText3,
                        RetailerErpId = r.RetailerErpId,
                        CompanyName = r.CompanyName,
                        CompanyGSTIN = r.CompanyGSTIN,
                        CompanyAddress = r.CompanyAddress,
                        BeatName = r.BeatName,
                        Date = r.Date,
                        InvoiceNumber = r.InvoiceNumber,
                        OrderTime = r.OrderTime,
                        LocationName = r.LocationName,
                        LocationAddress = r.LocationAddress,
                        LocationNumber = r.LocationNumber,
                        GSTIN = r.GSTIN,
                        AttributeNumber1 = r.AttributeNumber1?.ToString(),
                        AttributeNumber2 = r.AttributeNumber2?.ToString(),
                        AttributeNumber3 = r.AttributeNumber3?.ToString(),
                        AttributeText1 = r.AttributeText1,
                        AttributeText2 = r.AttributeText2,
                        AttributeText3 = r.AttributeText3,
                        PlaceofDelivery = r.PlaceofDelivery,
                        PlaceofSupply = r.PlaceofSupply,
                        Remark = r.Remark,
                        DataForSec = (r.DataForSec ?? new List<SaleDataDetails>()).Select(rr => new SaleDetails
                        {
                            ProductName = rr.ProductName,
                            UnitPrice = CurrencySymbol + rr.UnitPrice,
                            Quantity = UsesConversion
                                ? (rr.QtyInStdUnit > 0
                                    ? rr.QtyInStdUnit + " " + rr.StdUnit + " " + (rr.QtyInUnit > 0 ? rr.QtyInUnit + " " + rr.Unit : "")
                                    : rr.QtyInUnit + " " + rr.Unit)
                                : rr.Quantity.ToString("N2", indianCulture) + " " + rr.Unit,
                            Value = CurrencySymbol + rr.Value.ToString("N2", indianCulture),
                            PTR = CurrencySymbol + rr.PTR.ToString("N2", indianCulture),
                            MRP = rr.MRP.HasValue ? CurrencySymbol + rr.MRP.Value.ToString("N2", indianCulture) : null,
                            FOCQty = rr.FOCQty.ToString("N2", indianCulture) + " " + rr.Unit,
                            CESS = rr.CESS.HasValue && rr.CESS.Value > 0 ? CurrencySymbol + rr.CESS.Value.ToString("N2", indianCulture) : null,
                            ErpCode = rr.ErpCode,
                            GST = !string.IsNullOrEmpty(rr.GST) ? rr.GST + "%" : null,
                            GSTAmount = rr.GSTAmount.HasValue ? CurrencySymbol + rr.GSTAmount.Value.ToString("N2", indianCulture) : null,
                            TaxPercent = !string.IsNullOrEmpty(rr.TaxPercent) ? rr.TaxPercent + "%" : null,
                            TaxAmount = rr.TaxAmount.HasValue ? CurrencySymbol + rr.TaxAmount.Value.ToString("N2", indianCulture) : null,
                            HSNCode = rr.HSNCode,
                            Unit = rr.Unit,
                            QuantityInSuperUnit = UsesConversion
                                ? (rr.QtyInSuperUnit > 0 ? rr.QtyInSuperUnit.ToString("N2", indianCulture) + " " + rr.SuperUnit : "")
                                : rr.Quantity.ToString("N2", indianCulture) + " " + rr.Unit,
                            FOCValue = CurrencySymbol + rr.FOCValue.ToString("N2", indianCulture),
                            Discount = CurrencySymbol + rr.Discount.ToString("N2", indianCulture),
                            StockQty = rr.StockQty.ToString("N2", indianCulture) + " " + rr.Unit,
                            ReturnQty = rr.ReturnQty.ToString("N2", indianCulture) + " " + rr.Unit,
                            Closetoexp = rr.Closetoexp.ToString("N2", indianCulture) + " " + rr.Unit
                        }).ToList().Concat(new List<SaleDetails>
                        {
                            new SaleDetails
                            {
                                ProductName = "TOTAL",
                                Quantity = UsesConversion
                                    ? (
                                        (r.DataForSec.Sum(x => x.QtyInStdUnit) > 0 ? r.DataForSec.Sum(x => x.QtyInStdUnit) + " " + r.DataForSec.FirstOrDefault()?.StdUnit + " " : "")
                                        + (r.DataForSec.Sum(x => x.QtyInUnit) > 0 ? r.DataForSec.Sum(x => x.QtyInUnit) + " " + r.DataForSec.FirstOrDefault()?.Unit : "")
                                        ).Trim()
                                                : r.DataForSec.Sum(x => x.Quantity).ToString("N2", indianCulture) + " " + r.DataForSec.FirstOrDefault()?.Unit,
                                Value = CurrencySymbol + r.DataForSec.Sum(x => x.Value).ToString("N2", indianCulture),
                                PTR = CurrencySymbol + r.DataForSec.Sum(x => x.PTR).ToString("N2", indianCulture),
                                FOCValue = CurrencySymbol + r.DataForSec.Sum(x => x.FOCValue).ToString("N2", indianCulture),
                                CESS = CurrencySymbol + r.DataForSec.Sum(x => x.CESS ?? 0).ToString("N2", indianCulture),
                                GSTAmount = CurrencySymbol + r.DataForSec.Sum(x => x.GSTAmount ?? 0).ToString("N2", indianCulture),
                                TaxAmount = CurrencySymbol + r.DataForSec.Sum(x => x.TaxAmount ?? 0).ToString("N2", indianCulture),
                                Discount = CurrencySymbol + r.DataForSec.Sum(x => x.Discount).ToString("N2", indianCulture),
                                FOCQty = r.DataForSec.Sum(x => x.FOCQty).ToString("N2", indianCulture) + " " + r.DataForSec.FirstOrDefault()?.Unit,
                                ErpCode = "",
                                QuantityInSuperUnit = UsesConversion
                                    ? r.DataForSec.Sum(x => x.QtyInSuperUnit).ToString("N2", indianCulture) + " " + r.DataForSec.FirstOrDefault()?.SuperUnit
                                    : r.DataForSec.Sum(x => x.Quantity).ToString("N2", indianCulture) + " " + r.DataForSec.FirstOrDefault()?.Unit,
                                ReturnQty = r.DataForSec.Sum(x => x.ReturnQty) > 0
                                    ? r.DataForSec.Sum(x => x.ReturnQty).ToString("N2", indianCulture) + " " + r.DataForSec.FirstOrDefault()?.Unit
                                    : ""
                    }
                }).ToList(),

                                    RetailersCESS = r.DataForSec.Sum(s => s.CESS ?? 0) > 0
                    ? r.DataForSec.Sum(s => s.CESS ?? 0).ToString("N2", indianCulture)
                    : null,
                        RetailersQuantity = r.RetailersQuantity.ToString(),
                        RetailersOrderValue = r.RetailersOrderValue.ToString(),
                        RetailersOrderTax = CurrencySymbol + r.RetailerOrderTax.GetValueOrDefault().ToString("N2", indianCulture),
                        RetailerStockQty = r.RetailerStockQty.HasValue && r.RetailerStockQty > 0 ? r.RetailerStockQty.Value.ToString() : "0",
                        RetailerReturnQty = r.RetailerReturnQty.HasValue && r.RetailerReturnQty > 0 ? r.RetailerReturnQty.Value.ToString() : "0",
                        RetailerClosetoExp = r.RetailerClosetoExp.HasValue && r.RetailerClosetoExp > 0 ? r.RetailerClosetoExp.Value.ToString() : "0",
                        RetailersCashDiscount = r.RetailersCashDiscount.HasValue && r.RetailersCashDiscount > 0 ? CurrencySymbol + r.RetailersCashDiscount.Value.ToString() : null,
                        RetailersDiscount = r.RetailersDiscount.HasValue && r.RetailersDiscount > 0 ? CurrencySymbol + r.RetailersDiscount.Value.ToString() : null,
                        RetailersCGST = r.RetailersCGST.HasValue && r.RetailersCGST > 0 ? r.RetailersCGST.Value.ToString() : null,
                        RetailersSGST = r.RetailersSGST.HasValue && r.RetailersSGST > 0 ? r.RetailersSGST.Value.ToString() : null,
                        RetailersIGST = r.RetailersIGST.HasValue && r.RetailersIGST > 0 ? r.RetailersIGST.Value.ToString() : null,
                        RetailersSchemeDiscount = r.RetailersSchemeDiscount.HasValue && r.RetailersSchemeDiscount > 0 ? r.RetailersSchemeDiscount.Value.ToString() : null,
                        RetailersNetOrderValue = r.RetailersNetOrderValue.HasValue ? CurrencySymbol + r.RetailersNetOrderValue.Value.ToString() : CurrencySymbol + "0",
                        PaymentCollected = r.PaymentValue.HasValue && r.PaymentValue > 0 ? CurrencySymbol + r.PaymentValue.Value.ToString() : null,
                        ModeofPayment = r.ModeofPayment,
                        TotalDiscount = ((decimal)r.RetailersDiscount.GetValueOrDefault() + (decimal)r.RetailersCashDiscount.GetValueOrDefault() +
                                (decimal)r.RetailersSchemeDiscount.GetValueOrDefault()).ToString(),
                        TotalFOCBenefit = r.FOCBenefit?.ToString(),
                        TotalFocValue = r.FOCValue?.ToString(),
                        TotalTax = CurrencySymbol + r.RetailerOrderTax.GetValueOrDefault().ToString("N2", indianCulture),
                    }).ToList();

                    return abc;
                } 
                catch(Exception ex) {
                    throw ex;
                }
                }
            
        }

        private static decimal AggregateDecimal(IEnumerable<string> values)
        {
            return values
                .Select(v => decimal.TryParse(v, out var d) ? d : 0)
                .Sum();
        }

        private static decimal SumDecimalFields(IEnumerable<string> values)
        {
            return values.Sum(v =>
            {
                if (string.IsNullOrWhiteSpace(v))
                    return 0m;

                var cleaned = v.Replace(",", "").Replace("₹", "").Replace("$", "").Trim();

                return decimal.TryParse(cleaned, NumberStyles.Any, CultureInfo.InvariantCulture, out var result)
                    ? result
                    : 0m;
            });
        }


        private static string AggregateQuantity(IEnumerable<string> quantities)
        {
            var unitMap = new Dictionary<string, double>(StringComparer.OrdinalIgnoreCase);

            foreach (var quantity in quantities)
            {
                var (value, unit) = ExtractValueAndUnit(quantity);
                if (unit != null)
                {
                    if (unitMap.ContainsKey(unit))
                        unitMap[unit] += value;
                    else
                        unitMap[unit] = value;
                }
            }

            return string.Join(", ", unitMap.Select(kv => $"{kv.Value} {kv.Key}"));
        }

        private static string AggregateQuantityForQuantityString(IEnumerable<string> quantities)
        {
            var unitMap = new Dictionary<string, double>(StringComparer.OrdinalIgnoreCase);

            foreach (var quantity in quantities)
            {
                var pairs = ParseQuantityString(quantity);
                foreach (var (value, unit) in pairs)
                {
                    if (unitMap.ContainsKey(unit))
                        unitMap[unit] += value;
                    else
                        unitMap[unit] = value;
                }
            }
            return string.Join(", ", unitMap.Select(kv => $"{kv.Value:F2} {kv.Key}"));
        }

        private static List<(double value, string unit)> ParseQuantityString(string quantity)
        {
            var result = new List<(double value, string unit)>();
            var regex = new Regex(@"(\d+(?:\.\d+)?)\s*([A-Za-z]+)");
            var matches = regex.Matches(quantity);
            foreach (Match match in matches)
            {
                if (match.Success)
                {
                    if (double.TryParse(match.Groups[1].Value, out double value))
                    {
                        string unit = match.Groups[2].Value;
                        result.Add((value, unit));
                    }
                }
            }
            return result;
        }


        private static (double Value, string Unit) ExtractValueAndUnit(string input)
        {
            if (string.IsNullOrWhiteSpace(input)) return (0, null);

            // Extract numeric value
            var match = Regex.Match(input, @"(\d+(\.\d+)?)\s*([a-zA-Z]*)");
            if (match.Success)
            {
                double value = double.TryParse(match.Groups[1].Value, out double v) ? v : 0;
                string unit = match.Groups[3].Value.Trim();
                return (value, unit);
            }

            return (0, null);
        }

        private static decimal ExtractDecimalValue(string input)
        {
            if (string.IsNullOrWhiteSpace(input)) return 0;

            // Remove currency symbols (₹, $, etc.) and parse as decimal
            var cleaned = Regex.Replace(input, @"[^\d.]", "");
            return decimal.TryParse(cleaned, out decimal value) ? value : 0;
        }

    }
}
