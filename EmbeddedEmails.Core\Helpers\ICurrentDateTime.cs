﻿using System;
using System.Collections.Generic;
using System.Text;

namespace EmbeddedEmails.Core.Helpers
{
    public interface ICurrentDateTime
    {
        DateTime UtcNow { get; }
        DateTimeOffset IndiaNow { get; }
    }
    public class CurrentDateTime : ICurrentDateTime
    {
        private readonly DateTime now;

        public CurrentDateTime()
        {
            now = DateTime.UtcNow;
        }

        public DateTime UtcNow => now;
        public DateTimeOffset IndiaNow => new DateTimeOffset(now).ToOffset(TimeSpan.FromMinutes(330));
    }
}
