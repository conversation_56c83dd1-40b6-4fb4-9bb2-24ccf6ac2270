﻿using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using ShareOrderonEmail.Core.Models;
using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using ShareOrderonEmail.DbStorage.DbContexts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.DbStorage.Repositories.MasterRepositories
{
    public class CompanyRepository : ICompanyRepository
    {
        private readonly MasterDbContext db;

        private readonly List<string> SettingKeyValue = new List<string> { 
            "usesCloseToExpiry", 
            "ReasonForProductReturn",
            "IsUsesRetailerStock",  
        };
        public CompanyRepository(MasterDbContext db)
        {
            this.db = db;
        }
        public async Task<List<long>> GetAllActiveCompanies()
        {
            return await db.Companies.Where(c => !c.Deleted).Select(c => c.Id).ToListAsync();
        }

        public async Task<List<long>> GetAllActiveCompanyforSpecifiedSetting()
        {
            var setId = await db.CompanySettings.Where(s => s.SettingKey == "ShareOrderToDistributorViaEmail").Select(c => c.Id).FirstOrDefaultAsync();
            return await db.CompanySettingValues.Where(s => s.SettingId == setId && (s.SettingValue == "true" || s.SettingValue == "True")).Select(s => s.CompanyId).ToListAsync();
        }

        public async Task<bool> ShareOrderToDistributor(long companyId)
        {

            var DistProdDivBeatMappings = await db.CompanySettingValues.Where(c => c.CompanyId == companyId && c.Setting.SettingKey == "ShareOrderToDistributorViaEmail").Select(c => c.SettingValue).FirstOrDefaultAsync();
            DistProdDivBeatMappings = DistProdDivBeatMappings ?? await db.CompanySettings.Where(s => s.SettingKey == "ShareOrderToDistributorViaEmail").Select(s => s.DefaultValue).FirstOrDefaultAsync();
            bool toreturn = false;
            Boolean.TryParse(DistProdDivBeatMappings, out toreturn);

            return toreturn;
        }
        public async Task<string> ShareOrderToDistributorAt(long companyId)
        {

            var DistProdDivBeatMappings = await db.CompanySettingValues.Where(c => c.CompanyId == companyId && c.Setting.SettingKey == "ShareOrderEmailToDistributorAt").Select(c => c.SettingValue).FirstOrDefaultAsync();
            DistProdDivBeatMappings = DistProdDivBeatMappings ?? await db.CompanySettings.Where(s => s.SettingKey == "ShareOrderEmailToDistributorAt").Select(s => s.DefaultValue).FirstOrDefaultAsync();

            return DistProdDivBeatMappings.ToString();
        }
        public async Task<bool> UsesClosetoExpiry(long companyId)
        {

            var DistProdDivBeatMappings = await db.CompanySettingValues.Where(c => c.CompanyId == companyId && c.Setting.SettingKey == "usesCloseToExpiry").Select(c => c.SettingValue).FirstOrDefaultAsync();
            DistProdDivBeatMappings = DistProdDivBeatMappings ?? await db.CompanySettings.Where(s => s.SettingKey == "usesCloseToExpiry").Select(s => s.DefaultValue).FirstOrDefaultAsync();
            bool toreturn = false;
            Boolean.TryParse(DistProdDivBeatMappings, out toreturn);

            return toreturn;
        }
        public async Task<List<string>> ShowReturnDetailScreen(long companyId)
        {

            var DistProdDivBeatMappings = await db.CompanySettingValues.Where(c => c.CompanyId == companyId && c.Setting.SettingKey == "ReasonForProductReturn" && c.SettingValue != "[]").Select(c => c.SettingValue).ToListAsync();

            return DistProdDivBeatMappings;
        }
        public async Task<bool> UsesRetailerStock(long companyId)
        {

            var DistProdDivBeatMappings = await db.CompanySettingValues.Where(c => c.CompanyId == companyId && c.Setting.SettingKey == "IsUsesRetailerStock").Select(c => c.SettingValue).FirstOrDefaultAsync();
            DistProdDivBeatMappings = DistProdDivBeatMappings ?? await db.CompanySettings.Where(s => s.SettingKey == "IsUsesRetailerStock").Select(s => s.DefaultValue).FirstOrDefaultAsync();
            bool toreturn = false;
            Boolean.TryParse(DistProdDivBeatMappings, out toreturn);

            return toreturn;
        }
        public async Task<string> GetName(long companyId)
        {
            return await db.Companies.Where(c => c.Id == companyId).Select(c => c.Name).FirstOrDefaultAsync();
        }

        public async Task<CompanyData> GetCompanyDetails(long companyId)
        {
            return await db.Companies.Where(c => c.Id == companyId).Select(c => new CompanyData
            {
                AccountManagerEmail = c.AccountManagerEmail,
                Address = c.Address,    
                Deleted = c.Deleted,
                GSTIN = c.GSTIN,
                Id = companyId,
                KeyAccountManagerEmail = c.KeyAccountManagerEmail,
                LegalName = c.LegalName,
                Name = c.Name,
            }).FirstOrDefaultAsync();
        }

        public Dictionary<string, object> GetSettings(long companyId)
        {
            var settingsdb = db.CompanySettings.Where(s => !s.IsDeprecated)
                .GroupJoin(db.CompanySettingValues.Where(v => v.CompanyId == companyId).DefaultIfEmpty(),
                s => s.Id, v => v.SettingId, (s, v) => new { s.SettingKey, s.SettingType, s.DefaultValue, v.FirstOrDefault().SettingValue })
                .ToList();
            var settings = settingsdb.ToDictionary(k => k.SettingKey, v => ConvertFrom(v.SettingType, v?.SettingValue, v.DefaultValue));
            return settings;

        }
        private static object ConvertFrom(CompanySettingType settingType, string settingValue, string defaultValue)
        {
            switch (settingType)
            {
                case CompanySettingType.Boolean:
                    bool boolValue;
                    if (!string.IsNullOrWhiteSpace(settingValue) && bool.TryParse(settingValue, out boolValue))
                        return boolValue;
                    else
                        return bool.Parse(defaultValue);
                case CompanySettingType.Decimal:
                    double decimalValue;
                    if (!string.IsNullOrWhiteSpace(settingValue) && double.TryParse(settingValue, out decimalValue))
                        return decimalValue;
                    else
                        return double.Parse(defaultValue);
                case CompanySettingType.Integer:
                    long intValue;
                    if (!string.IsNullOrWhiteSpace(settingValue) && long.TryParse(settingValue, out intValue))
                        return intValue;
                    else
                        return int.Parse(defaultValue);
                case CompanySettingType.TextList:
                    if (string.IsNullOrWhiteSpace(settingValue)) return new List<string>();
                    List<string> stringlist = JsonConvert.DeserializeObject<List<string>>(settingValue);
                    return stringlist;
                case CompanySettingType.Text:
                default:
                    return settingValue;
            }

        }
        public String GetCountryDetails(string country)
        {
            return db.CountryDetails.Where(d => d.CountryName == country).Select(s=> s.CurrencySymbol).FirstOrDefault();
        }
    }
}
