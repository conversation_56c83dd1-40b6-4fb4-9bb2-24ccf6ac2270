﻿using EmbeddedEmails.Core.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ReportSender.Configuration;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace EmbeddedEmails.Tests
{
    [TestClass]
    public class ReportSubscriptionDbTest
    {
        private ServiceProvider provider;

        [TestInitialize]
        public void Initialise()
        {

            Environment.SetEnvironmentVariable("BuildEnvironment", "ManageTesting");

            var env = System.Environment.GetEnvironmentVariable("BuildEnvironment");
            var config = new ConfigurationBuilder()
             .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
             .AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true)
             .AddEnvironmentVariables()
             .Build();

            IServiceCollection serviceCollection = new ServiceCollection();
            Dependencies.SetUp(serviceCollection,config);

            provider = serviceCollection.BuildServiceProvider();
        }

        [TestMethod]
        public async Task TestMethod1()
        {
            var managerRepo = provider.GetRequiredService<IManagerRepository>();
            var userUIPreferences = provider.GetRequiredService<IUserUIPreferences>();
            var userPreferences = await userUIPreferences.GetUserUIPreferences();
            var managers = (await managerRepo.GetAllManagersForSubscriptions(userPreferences)).ToList();
            Console.WriteLine($"UserRole,UserId,Name,Email,CompanyId");
            foreach (var manager in managers)
            {
                Console.WriteLine($"{manager.UserRole},{manager.UserId},{manager.Name},{manager.Email},{manager.CompanyId}");
            }

        }
    }
}
