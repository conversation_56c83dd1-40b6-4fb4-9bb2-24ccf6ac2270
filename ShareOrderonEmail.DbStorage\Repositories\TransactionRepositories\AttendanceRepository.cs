﻿using System;
using System.Collections.Generic;
using System.Text;
using ShareOrderonEmail.Core.Repositories.ITransactionRepositories;
using ShareOrderonEmail.DbStorage.DbContexts;
using ShareOrderonEmail.DbStorage.DbModels.TransactionDbModels;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using ShareOrderonEmail.Core.Models;

namespace ShareOrderonEmail.DbStorage.Repositories.TransactionRepositories
{
    public class AttendanceRepository : IAttendanceRepository
    {
        private readonly TransactionDbContext db;
        public AttendanceRepository(TransactionDbContext db)
        {
            this.db = db;
        }
        public async Task<List<AttendanceCore>> GetDataForCurrentDay(long companyId, DateTime fromDate, DateTime toDate)
        {
            var h = await db.Attendances.Where(a => a.CompanyId == companyId && a.IsValid == true && a.Productive == true && a.DeviceTime > fromDate && a.DeviceTime <= toDate).Select(d => new AttendanceCore
            {
                Id = d.Id,
                OrderInRevenue = d.OrderInRevenue,
                OrderInUnits = d.OrderInUnits,
                DistributorId = d.DistributorId,
                EmployeeId = d.EmployeeId,
                LocationId = d.LocationId,
                BeatId = d.BeatId,
                RemarksDistributor = d.RemarksDistributor,
                IGST = d.IGST,
                CGST = d.CGST,
                SGST = d.SGST,
                VAT = d.VAT,
                Excise = d.Excise,
                InvoiceNo = d.InvoiceNo,
                DeviceTime = d.DeviceTime,
                Discount = d.Discount,
                DiscountInAmount = d.DiscountInAmount,
            }).ToListAsync();
            return h;
        }

    }
}
