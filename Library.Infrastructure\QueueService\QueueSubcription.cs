﻿using System.Collections.Generic;


namespace Library.Infrastructure.QueueService
{
    public enum QueueType
    {
        SendEmail = 1,
        SendSMS = 2,
        SlackAutoBug = 3,
        SlackGeneric = 4,
        UpdateEvent = 5,
        UpdateDemoCompany = 6,
        UpdateEventPoison = 7,
        StaleDataTrigger = 8,
        StaleDataTriggerRetry = 9,
        DSRQueue=10,
        SendEmbeddedEmail=11,
        SlackCloudLogs = 12,
    }
    public class QueueSubcription
    {
        public string Queuestr { get; private set; }
        public QueueType QueueType { get; private set; }

        public QueueSubcription(QueueType queueType)
        {
            this.Queuestr = queueForEvents[queueType];
            this.QueueType = queueType;
        }

        private static Dictionary<QueueType, string> queueForEvents = new Dictionary<QueueType, string>
        {
            { QueueType.SendEmail,"email-queue" },
            { QueueType.SendEmbeddedEmail,"email-embedded-queue" },
            { QueueType.SendSMS,"sms-queue" },
            { QueueType.SlackAutoBug,"slack-autobug-queue" },
            { QueueType.SlackGeneric,"slack-queue" },
            { QueueType.UpdateEvent,"updateevent-queue" },
            { QueueType.UpdateDemoCompany,"updatdemocompany-queue" },
            { QueueType.UpdateEventPoison,"updateevent-queue-poison"},
            { QueueType.StaleDataTrigger,"staledata-queue"},
            { QueueType.StaleDataTriggerRetry,"staledata-queue-retry"},
            { QueueType.DSRQueue,"dsrrequest-queue"},
            { QueueType.SlackCloudLogs, "slack-queue-facloudlogs" },
        };

    }
}
