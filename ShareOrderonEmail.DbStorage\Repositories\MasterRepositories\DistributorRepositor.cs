﻿using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using ShareOrderonEmail.DbStorage.DbContexts;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using ShareOrderonEmail.Core.Models;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace ShareOrderonEmail.DbStorage.Repositories.MasterRepositories
{
    public class DistributorRepositor : IDistributorRepository
    {
        private readonly MasterDbContext db;
        public DistributorRepositor(MasterDbContext db)
        {
            this.db = db;
        }
        public async Task<Dictionary<long, DistributorCore>> GetDistributorDetails(long companyId, List<long> DistributorIds)
        {
            var dd = await db.Distributors.Where(d => d.CompanyId == companyId && DistributorIds.Contains(d.Id)).ToDictionaryAsync(a => a.Id, a => new DistributorCore
            {
                Name = a.Name,
                EmailId = a.EmailId,
                ContactNo = a.ContactNo,
                Address = a.Address,
                PlaceOfSupply = a.PlaceOfSupply,
                GSTIN = a.GSTIN,
                ClientSideId = a.ClientSideId, // erp id
                Distributor_AttributeNumber1 = a.Distributor_AttributeNumber1,
                Distributor_AttributeNumber2 = a.Distributor_AttributeNumber2,  
                Distributor_AttributeText1 = a.Distributor_AttributeText1,
                Distributor_AttributeText2 = a.Distributor_AttributeText2,
                Distributor_AttributeText3 = a.Distributor_AttributeText3,
            });
            return dd;
        }

        public async Task<List<long>> GetPdfConfigs(long companyId)
        {
            return await db.DistributorOrderPdfConfigs.Where(config => config.CompanyId == companyId && !config.IsVisible).Select(item => item.OrderSummaryPdfConfigId).ToListAsync();
        }

        public async Task<Dictionary<long, string>> GetPdfConfigsForDisplayName(long companyId)
        {
            return await db.DistributorOrderPdfConfigs.Where(config => config.CompanyId == companyId).ToDictionaryAsync(val => val.OrderSummaryPdfConfigId, val => val.DisplayName);
        }
    }
}
