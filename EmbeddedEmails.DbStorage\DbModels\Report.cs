﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Text;

namespace EmbeddedEmails.DbStorage.DbModels
{
    public class Report
    {
        public long Id { get; set; }
        public int ReportType { get; set; }
        public bool IsDeactive { get; set; }
    }

    public class ReportSubscription
    {
        public long Id { get; set; }
        public long ReportId { get; set; }
        public long CompanyId { get; set; }
        public bool IsDeactive { get; set; }
        public Guid EncryptionKey { get; set; }
        public PortalUserRole PortalUserRole { get; set; }
        public virtual Report Report { get; set; }
    }

}
