﻿using Microsoft.EntityFrameworkCore;
using ShareOrderonEmail.Core.Models;
using ShareOrderonEmail.Core.Repositories.ITransactionRepositories;
using ShareOrderonEmail.DbStorage.DbContexts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.DbStorage.Repositories.TransactionRepositories
{
    public class SchemeSaleRepository: ISchemeSaleRepository
    {
        private readonly TransactionDbContext db;
        public SchemeSaleRepository(TransactionDbContext db)
        {
            this.db = db;
        }

        public async Task<Dictionary<long,List<SchemeSalesCore>>> GetSchemeFocDetails(List<long> AttendanceIds)
        {
            var hh = await db.SchemeSales.Include(s => s.SchemeSaleItems).Where(s => AttendanceIds.Contains(s.AttendanceId)).GroupBy(a=> a.AttendanceId).ToListAsync();

            var kk = hh.Select(s => s.SelectMany(ss => ss.SchemeSaleItems));

            return hh.ToDictionary(s => s.Key, s => s.SelectMany(ss=> ss.SchemeSaleItems.Select(a => new SchemeSalesCore
            {
                ProductId = a.ProductId,
                FocQty = a.FOCQty,
                FocValue = a.FOCValue,
                FocPTR = a.PTRFOC,
            })).ToList());

            //return hh.ToDictionary(s => s.Key, s => s.FirstOrDefault().SchemeSaleItems.Select(a => new SchemeSalesCore
            //{
            //    ProductId = a.ProductId,
            //    FocQty = a.FOCQty,
            //    FocValue = a.FOCValue
            //}).ToList());

        }
    }
}
