<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="12.1.0" />
    <PackageReference Include="EPPlus.Core" Version="1.5.4" />
    <PackageReference Include="FastMember" Version="1.3.0" />
    <PackageReference Include="iTextSharp.LGPLv2.Core" Version="1.4.2" />
    <PackageReference Include="Newtonsoft.Json" Version="11.0.1" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
    <ProjectReference Include="..\Library.DateTimeHelpers\Library.DateTimeHelpers.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Fonts\NotoSans-Regular.ttf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
