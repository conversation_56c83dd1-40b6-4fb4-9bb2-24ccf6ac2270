﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.DbStorage.DbModels.MasterDbModels
{
    public class DistributorOrderPdfConfigs
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public long OrderSummaryPdfConfigId { get; set; }

        public string DisplayName { get; set; }

        public bool IsVisible { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }
        
        public DateTime CreatedAt { get; set; }
    }
}
