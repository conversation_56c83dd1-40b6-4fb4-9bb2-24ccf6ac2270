﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace ShareOrderonEmail.DbStorage.DbModels.MasterDbModels
{
    public class Location
    {
        public long Id { get; set; }
        [Column("Company")]
        public long CompanyId { get; set; }
        public string ShopName { get; set; }
        public string Address { get; set; }
        public string OwnersName { get; set; }
        public string OwnersNo { get; set; }
        public string GSTIN { get; set; }
        public string PlaceOfDelivery { set; get; }
        public string ErpId { set; get; }
        public string AttributeText1 { get; set; }
        public string AttributeText2 { get; set; }
        public string AttributeText3 { get; set; }
        public double? AttributeNumber1 { get; set; }
        public double? AttributeNumber2 { get; set; }
        public double? AttributeNumber3 { get; set; }
    }
}
