﻿using EmbeddedEmails.Core.DbModels;
using EmbeddedEmails.Core.Repositories;
using EmbeddedEmails.DbStorage.DbContexts;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EmbeddedEmails.DbStorage.Repositories
{
    public class DayRepository : IDayRepository
    {
        private readonly ReportDbContext db;

        public DayRepository(ReportDbContext db)
        {
            this.db = db;
        }

        private IQueryable<DayStart> GetDayStartQueryForUser(long companyId, PortalUserRole userRole, long userId)
        {
            return db.DayStarts.Where(d => d.CompanyId == companyId).Where(d => (userRole == PortalUserRole.AreaSalesManager && d.ASMId == userId)
                               || (userRole == PortalUserRole.RegionalSalesManager && d.RSMId == userId)
                               || (userRole == PortalUserRole.ZonalSalesManager && d.ZSMId == userId)
                               || (userRole == PortalUserRole.NationalSalesManager && d.NSMId == userId)
                               || (userRole == PortalUserRole.GlobalSalesManager && d.GSMId == userId)
                               || (userRole == PortalUserRole.CompanyAdmin || userRole == PortalUserRole.GlobalAdmin
                               || userRole == PortalUserRole.ChannelPartner || userRole == PortalUserRole.AccountManager
                               || userRole == PortalUserRole.CompanyExecutive));
        }

        private IQueryable<DayStart> GetDayStartQueryWithDateRangeForUsers(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey)
        {
            return GetDayStartQueryForUser(companyId, userRole, userId).Where(d => d.DayStartDateKey <= toDateKey && d.DayStartDateKey >= fromDateKey);
        }

        public async Task<List<DayStart>> GetDaySummary(long companyId, PortalUserRole userRole, long userId, long dateKey)
        {
            return await GetDayStartQueryForUser(companyId, userRole, userId).Where(d => d.DayStartDateKey == dateKey).ToListAsync();
        }

        public async Task<int> GetMaxActiveEmpForDayIndateRange(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey)
        {
            return await GetDayStartQueryWithDateRangeForUsers(companyId, userRole, userId, fromDateKey, toDateKey)
                .GroupBy(d => d.DayStartDateKey)
                .MaxAsync(d => d.Count(p => p.DayStartType != DayStartType.None));
        }

        public async Task<int> GetMaxPcForDayIndateRange(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey)
        {
            return await GetDayStartQueryWithDateRangeForUsers(companyId, userRole, userId, fromDateKey, toDateKey)
               .GroupBy(d => d.DayStartDateKey)
               .MaxAsync(d => d.Sum(p => p.PC));
        }

        public async Task<int> GetMaxScForDayIndateRange(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey)
        {
            return await GetDayStartQueryWithDateRangeForUsers(companyId, userRole, userId, fromDateKey, toDateKey)
              .GroupBy(d => d.DayStartDateKey)
              .MaxAsync(d => d.Sum(p => p.SC));
        }

        public async Task<int> GetMaxTcForDayIndateRange(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey)
        {
            return await GetDayStartQueryWithDateRangeForUsers(companyId, userRole, userId, fromDateKey, toDateKey)
              .GroupBy(d => d.DayStartDateKey)
              .MaxAsync(d => d.Sum(p => p.TC));
        }


        public async Task<List<DayStart>> GetDateRangeSummary(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey)
        {
            return await GetDayStartQueryWithDateRangeForUsers(companyId, userRole, userId, fromDateKey, toDateKey).ToListAsync();
        }
    }
}
