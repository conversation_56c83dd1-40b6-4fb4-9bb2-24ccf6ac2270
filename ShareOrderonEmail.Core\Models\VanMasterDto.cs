﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace ShareOrderonEmail.Core.Models
{
    public class VanMasterDto
    {
        public long Id { get; set; }

        public string VanName { get; set; }

        public string VanRegistrationNumber { get; set; }

        public string ChassisNumber { get; set; }

        public double VanCapacity { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string CreationContext { get; set; }

        public bool Deleted { get; set; }

        public string VanInvoicePrefix { get; set; }

        public long? CompanyId { get; set; }
    }
}
