﻿using SlackNet;
using SlackNet.WebApi;
using System.Net.Http;
using Library.SlackService.Interface;
using System;
using System.Threading.Tasks;
using System.IO;
using System.Collections.Generic;
using Library.CommonHelpers;

namespace Library.SlackService
{
    public class SlackLogHelper : ISlackLogHelper
    {
        private readonly HttpClient _httpClient;
        private readonly ISlackApiClient _slackClient;
        private readonly string _channelId;

        public SlackLogHelper(IHttpClientFactory httpClientFactory, ISlackApiClient slackClient, string masterStorageConnectionString, string channelId)
        {
            _httpClient = httpClientFactory.CreateClient();
            _slackClient = slackClient;
            _channelId = channelId;
            _slackClient.Conversations.Join(channelId);
        }

        public async Task SendMessageToSlack(string message)
        {
            try
            {
                await _slackClient.Chat.PostMessage(new Message
                {
                    Channel = _channelId,
                    IconEmoji = ":robot_face:",
                    Text = message,
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to send log to <PERSON>lack: {ex}");
            }
        }

        private async Task UploadLogs(FileInfo logFile, string slackMessage, string channelId = null)
        {
            channelId = channelId ?? _channelId;
            await _slackClient.Conversations.Join(channelId);
            var uploadURL = await _slackClient.Files.GetUploadUrlExternal(logFile.Name, (int)logFile.Length);
            await APICallHelper.UploadFile(_httpClient, uploadURL.UploadUrl, logFile);
            await _slackClient.Files.CompleteUploadExternal(new List<ExternalFileReference> { new ExternalFileReference { Id = uploadURL.FileId, Title = logFile.Name } },
                channelId, initialComment: slackMessage);
        }

        public async Task<bool> SendLogAndFile(FileInfo logFile, string slackMessage, string channelId = null, bool compressFile = false)
        {
            try
            {
                await SendMessageToSlack(slackMessage + $" File: {logFile.Name}");
                
                await UploadLogs(logFile, slackMessage, channelId);
                
                return true;
            }
            catch (Exception ex)
            {
                var failureMessage = $"Failed to Send Log and/or File:\n {ex}";
                Console.WriteLine(failureMessage);
                await SendMessageToSlack(failureMessage);
                return false;
            }
        }
    }
}
