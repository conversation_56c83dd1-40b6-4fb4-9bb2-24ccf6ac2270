﻿
using System.Collections.Concurrent;
using System.IO;
using System.Threading.Tasks;
using System;

namespace FileLogger
{
    public class AsyncFileLogger : IDisposable
    {
        private string LogFileName;
        private string MetadataToAddToLogs;
        private FileStream LogFileStream;
        private StreamWriter LogFileWriter;
        private Task processQueueTask;

        private readonly BlockingCollection<string> entryQueue = new BlockingCollection<string>(1024);

        public Task InitializeAsync(string logFileName, bool append = false, string metadataToAddToLogs = "")
        {
            if (!string.IsNullOrWhiteSpace(LogFileName))
            {
                throw new InvalidOperationException("Cannot Re-initialize FileLogger");
            }
            if (string.IsNullOrWhiteSpace(logFileName))
            {
                throw new ArgumentNullException("LogFileName cannot be null. First initialize it using Initialize Method");
            }
            LogFileName = logFileName;
            MetadataToAddToLogs = metadataToAddToLogs;

            CreateFile(append);

            processQueueTask = Task.Run(() => ProcessQueue());

            return Task.CompletedTask;
        }

        private void CreateFile(bool append)
        {
#pragma warning disable CS8604 // Possible null reference argument.
            var fileInfo = new FileInfo(LogFileName);
#pragma warning restore CS8604 // Possible null reference argument.
#pragma warning disable CS8602 // Dereference of a possibly null reference.
            fileInfo.Directory.Create();
#pragma warning restore CS8602 // Dereference of a possibly null reference.

            LogFileStream = new FileStream(LogFileName, FileMode.OpenOrCreate, FileAccess.Write, FileShare.Read);
            if (append)
            {
                LogFileStream.Seek(0, SeekOrigin.End);
            }
            else
            {
                LogFileStream.SetLength(0); // clear the file
            }
            LogFileWriter = new StreamWriter(LogFileStream);
        }

        private void WriteMessage(string message, bool flush)
        {
            if (LogFileWriter != null)
            {
                LogFileWriter.WriteLine(message);
                if (flush)
                    LogFileWriter.Flush();
            }
        }

        private Task ProcessQueue()
        {
            var writeMessageFailed = false;
            foreach (var message in entryQueue.GetConsumingEnumerable())
            {
                try
                {
                    if (!writeMessageFailed)
                    {
                        WriteMessage(message, entryQueue.Count == 0);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to write into file {LogFileName}.\n{ex}");
                }
            }

            return Task.CompletedTask;
        }

        public void WriteLine(string message, bool skipConsoleLog = false, bool truncateConsoleMessage = false, short truncatedLength = 256)
        {
            if (string.IsNullOrWhiteSpace(LogFileName))
            {
                throw new ArgumentNullException("LogFileName cannot be null. First initialize it using Initialize Method");
            }
            if (!entryQueue.IsAddingCompleted)
            {
                message = $"[{DateTime.UtcNow.AddMinutes(330)}]: {MetadataToAddToLogs} {message}";
                if (!skipConsoleLog)
                {
                    Console.WriteLine(truncateConsoleMessage ? message.Substring(0, Math.Min(truncatedLength, message.Length)) : message);
                }
                try
                {
                    entryQueue.Add(message);
                    return;
                }
                catch (InvalidOperationException) { }
            }
            else
            {
                throw new ObjectDisposedException("Cannot log in a disposed FileLogger");
            }
        }

        public async Task FlushAsync()
        {
            if (processQueueTask != null)
            {
                Console.WriteLine($"Waiting for logging all entries to File {LogFileName}!!");
                await processQueueTask;
                Console.WriteLine($"Finished logging all entries to File!! {LogFileName}");
            }
        }

        public void Dispose()
        {
            entryQueue.CompleteAdding();
            FlushAsync().Wait();
            if (LogFileWriter != null)
            {
                LogFileWriter.Dispose();
            }
        }
    }

}
