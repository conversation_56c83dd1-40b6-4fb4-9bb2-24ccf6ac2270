﻿using EmbeddedEmails.Core.Repositories;
using EmbeddedEmails.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EmbeddedEmails.DbStorage.Repositories
{
    public class BeatRepository : IBeatRepository
    {
        private readonly MasterDbContext db;

        public BeatRepository(MasterDbContext db)
        {
            this.db = db;
        }

        public async Task<Dictionary<long, string>> GetMin(List<long> ids)
        {
            return (await db.Beats.Where(a => ids.Contains(a.Id)).ToListAsync()).ToDictionary(a => a.Id, a => a.Name);
        }
    }
}
