﻿# Steps for Setting up the FA_EmailWebjobs repository:
#### **Prerequisite**: _Access to the repository and FA databases and **Vault Access**, and Visual Studio Community installed._
1. Clone the repository on Visual Studio through azure login.
2. Now open FA_EmailWebJobs.sln > Set EmbeddedEmails as a start up Project.
3. In VS>tools>options>Azure Authentication Service>Choose your f2k azure account > OK. (Do Login in Windows Work Account too with your f2k profile)
4. To Run the EmbeddedEmails
   - Go to EmbeddedEmails.Tests
   - Open EmailDataTest.cs
   - Set KEYVAULT_ENDPOINT : "https://v3DebugWritable.vault.azure.net/"
5. Change the recipientName, User Role, UserId, CompanyId and Date details under required test method
5. Select the required Test method and right click and click on debug test.