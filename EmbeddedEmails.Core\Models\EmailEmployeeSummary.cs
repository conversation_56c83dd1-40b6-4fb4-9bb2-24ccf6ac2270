﻿using EmbeddedEmails.Core.DbModels;
using FileGenerator.Attributes;
using Libraries.CommonEnums;
using Library.DateTimeHelpers;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace EmbeddedEmails.Core.Models
{
    public class ColumnHeader : Attribute
    {
        public string Name { get; set; }
        public ColumnHeader(string name)
        {
            Name = name;
        }
    }
    //public class EmailEmployeeSummary
    // {
    //     public string Name { get; set; }
    //     public string Email { get; set; }
    //     public List<EmployeeSummaryDTO> EmployeeSummaries { get; set; }
    // }
    public class EmployeeSummaryDTO
    {
        [TableField(ColumnName: "Id", NomenclatureRequirement = false)]
        public int Id { get; set; }
        [ColumnHeader("$$GSM$$")]
        [TableField(ColumnName: "GSM", NomenclatureRequirement = false)]
        public string GSM { get; set; }
        [ColumnHeader("$$NSM$$")]
        [TableField(ColumnName: "NSM", NomenclatureRequirement = false)]
        public string NSM { get; set; }
        [ColumnHeader("$$ZSM$$")]
        [TableField(ColumnName: "ZSM", NomenclatureRequirement = false)]
        public string ZSM { get; set; }
        [ColumnHeader("$$RSM$$")]
        [TableField(ColumnName: "RSM", NomenclatureRequirement = false)]
        public string RSM { get; set; }
        [ColumnHeader("Reporting Manager")]
        [TableField(ColumnName: "Reporting Manager", NomenclatureRequirement = false)]
        public string ReportingManager { get; set; }
        [ColumnHeader("$$ASM$$")]
        [TableField(ColumnName: "ASM", NomenclatureRequirement = false)]
        public string ASM { get; set; }
        [ColumnHeader("$$ESM$$ Name")]
        [TableField(ColumnName: "ESM", NomenclatureRequirement = false)]
        public string ESMName { get; set; }
        [ColumnHeader("Day Start Date")]
        [TableField(ColumnName: "Day Start Date", NomenclatureRequirement = false)]
        public DateTimeOffset DayStartDate { get; set; }
        [ColumnHeader("Day Start Time")]
        [TableField(ColumnName: "Day Start Time", NomenclatureRequirement = false)]
        public DateTimeOffset? DayStartTime { get; set; }
        [ColumnHeader("Day End Time")]
        [TableField(ColumnName: "Day Start Time", NomenclatureRequirement = false)]
        public DateTimeOffset? DayEndTime { get; set; }
        [ColumnHeader("Day End Type")]
        [TableField(ColumnName: "Day End Type", NomenclatureRequirement = false)]
        public DayEndType DayEndType { get; set; }
        [ColumnHeader("First Call Time")]
        [TableField(ColumnName: "First Call Time", NomenclatureRequirement = false)]
        public DateTimeOffset? FirstCallTime { get; set; }
        [ColumnHeader("First Productive Call Time")]
        [TableField(ColumnName: "First Productive Call Time", NomenclatureRequirement = false)]
        public DateTimeOffset? FirstProductiveCallTime { get; set; }
        [ColumnHeader("Retail Time in Minutes")]
        [TableField(ColumnName: "Retail Time in Minutes", NomenclatureRequirement = false)]
        public double TimeSpentRetailinginMinutes { get; set; }
        [TableField(ColumnName: "SC", NomenclatureRequirement = false)]
        public int SC { get; set; }
        [TableField(ColumnName: "TC", NomenclatureRequirement = false)]
        public int TC { get; set; }
        [TableField(ColumnName: "PC", NomenclatureRequirement = false)]
        public int PC { get; set; }
        [TableField(ColumnName: "OVC", NomenclatureRequirement = false)]
        public int OVC { get; set; }
        [ColumnHeader("Order Value")]
        [TableField(ColumnName: "Order Value", NomenclatureRequirement = false)]
        public double OrderBookingInRevenue { get; set; }
        [ColumnHeader("MTD Booking In Revenue")]
        [TableField(ColumnName: "MTD Booking In Revenue", NomenclatureRequirement = false)]
        public double MTDBookingInRevenue { get; set; }
        [ColumnHeader("Selected $$Beat$$")]
        [TableField(ColumnName: "Selected Beat", NomenclatureRequirement = false)]
        public string SelectedBeat { get; set; }
        [ColumnHeader("$$ESM$$ HQ")]
        [TableField(ColumnName: "ESM HQ", NomenclatureRequirement = false)]
        public string ESMHQ { get; set; }
        [ColumnHeader("$$Region$$")]
        [TableField(ColumnName: "Region", NomenclatureRequirement = false)]
        public string Region { get; set; }
        [ColumnHeader("$$Zone$$")]
        [TableField(ColumnName: "Zone", NomenclatureRequirement = false)]
        public string Zone { get; set; }
        [ColumnHeader("$$ESM$$ Erp Id")]
        [TableField(ColumnName: "ESM Erp Id", NomenclatureRequirement = false)]
        public string ESM_ERP_ID { get; set; }
        [ColumnHeader("$$ESM$$ Contact No")]
        [TableField(ColumnName: "ESM Contact No", NomenclatureRequirement = false)]
        public string ESMContactNo { get; set; }
        [TableField(ColumnName: "OVT", NomenclatureRequirement = false)]
        public int OVT { get; set; }
        [TableField(ColumnName: "CAP", NomenclatureRequirement = false)]
        public double CAP { get; set; }
        [ColumnHeader("Adherence (%)")]
        [TableField(ColumnName: "Adherence (%)", NomenclatureRequirement = false)]
        public double Adherence { get; set; }
        [ColumnHeader("Last Call Time")]
        [TableField(ColumnName: "Last Call Time", NomenclatureRequirement = false)]
        public DateTimeOffset? LastCallTime { get; set; }
        [ColumnHeader("Last Productive Call Time")]
        [TableField(ColumnName: "Last Productive Call Time", NomenclatureRequirement = false)]
        public DateTimeOffset? LastProductiveCallTime { get; set; }
        [ColumnHeader("Total Time In Minutes")]
        [TableField(ColumnName: "Total Time In Minutes", NomenclatureRequirement = false)]
        public double TotalTimeInMinutes { get; set; }
        [ColumnHeader("Is Late")]
        [TableField(ColumnName: "Is Late", NomenclatureRequirement = false)]
        public bool IsLate { get; set; }
        [ColumnHeader("Order Booking In Unit")]
        [TableField(ColumnName: "Order Booking In Unit", NomenclatureRequirement = false)]
        public double OrderBookingInUnit { get; set; }
        [ColumnHeader("Order Booking In StdUnit")]
        [TableField(ColumnName: "Order Booking In StdUnit", NomenclatureRequirement = false)]
        public double OrderBookingInStdUnit { get; set; }
        [ColumnHeader("MTD Booking in Unit")]
        [TableField(ColumnName: "MTD Booking in Unit", NomenclatureRequirement = false)]
        public double MTDBookinginUnit { get; set; }
        [ColumnHeader("MTD Booking In StandardUnit")]
        [TableField(ColumnName: "MTD Booking In StandardUnit", NomenclatureRequirement = false)]
        public double MTDBookingInStandardUnit { get; set; }
        [ColumnHeader("Total Scheme Discount")]
        [TableField(ColumnName: "Total Scheme Discount", NomenclatureRequirement = false)]
        public double TotalSchemeDiscount { get; set; }
        [ColumnHeader("Total SchemeQty")]
        [TableField(ColumnName: "Total SchemeQty", NomenclatureRequirement = false)]
        public double TotalSchemeQty { get; set; }
        [ColumnHeader("Total Product Wise Discount")]
        [TableField(ColumnName: "Total Product Wise Discount", NomenclatureRequirement = false)]
        public double TotalProductWiseDiscount { get; set; }
        [ColumnHeader("Lines Cut")]
        [TableField(ColumnName: "Lines Cut", NomenclatureRequirement = false)]
        public double LinesCut { get; set; }
        [ColumnHeader("No Of Styles")]
        [TableField(ColumnName: "No Of Styles", NomenclatureRequirement = false)]
        public int NoOfStyles { get; set; }
        [ColumnHeader("No Of Sec Category")]
        [TableField(ColumnName: "No Of Sec Category", NomenclatureRequirement = false)]
        public int NoOfSecCategory { get; set; }
        [ColumnHeader("Scheme Effective Calls")]
        [TableField(ColumnName: "Scheme Effective Calls", NomenclatureRequirement = false)]
        public int SchemeEffectiveCalls { get; set; }
        [ColumnHeader("TO")]
        [TableField(ColumnName: "TO", NomenclatureRequirement = false)]
        public int TelephonicOrders { get; set; }
        [ColumnHeader("New Outlets")]
        [TableField(ColumnName: "New Outlets", NomenclatureRequirement = false)]
        public int NewOutlets { get; set; }
        [ColumnHeader("New Outlets Sales in Unit")]
        [TableField(ColumnName: "New Outlets Sales in Unit", NomenclatureRequirement = false)]
        public double NewOutletsSalesinUnit { get; set; }
        [ColumnHeader("New Outlets Sales in StdUnit")]
        [TableField(ColumnName: "New Outlets Sales in StdUnit", NomenclatureRequirement = false)]
        public double NewOutletsSalesinStdUnit { get; set; }
        [ColumnHeader("New Outlets Revenue")]
        [TableField(ColumnName: "New Outlets Revenue", NomenclatureRequirement = false)]
        public double NewOutletsRevenue { get; set; }
        [TableField(ColumnName: "LPC", NomenclatureRequirement = false)]
        public double LPC { get; set; }
        [ColumnHeader("Styles Per call")]
        [TableField(ColumnName: "Styles Per call", NomenclatureRequirement = false)]
        public double StylesPercall { get; set; }
        [ColumnHeader("SC Per Call")]
        [TableField(ColumnName: "SC Per Call", NomenclatureRequirement = false)]
        public double SCPerCall { get; set; }
        [ColumnHeader("Assigned Type")]
        [TableField(ColumnName: "AssignedReasonCategory", NomenclatureRequirement = false)]
        public string AssignedReasonCategory { get; set; }
        [ColumnHeader("Assigned $$Beat$$")]
        [TableField(ColumnName: "Assigned Beat", NomenclatureRequirement = false)]
        public string AssignedBeat { get; set; }
        [ColumnHeader("Day Started")]
        [TableField(ColumnName: "Day Started", NomenclatureRequirement = false)]
        public bool DayStarted { get; set; }
        [ColumnHeader("Day Start Type")]
        [TableField(ColumnName: "DayStartType", NomenclatureRequirement = false)]
        public string DayStartType { get; set; }
        [ColumnHeader("Day Start Type Enum")]
        [TableField(ColumnName: "Day Start Type Enum", NomenclatureRequirement = false)]
        public DayStartType DayStartTypeEnum { get; set; }
        [TableField(ColumnName: "Reason", NomenclatureRequirement = false)]
        public string Reason { get; set; }
        [ColumnHeader("Assigned Reason")]
        [TableField(ColumnName: "AssignedReason", NomenclatureRequirement = false)]
        public string AssignedReason { get; set; }
        [ColumnHeader("Retail Time")]
        [TableField(ColumnName: "Retail Time", NomenclatureRequirement = false)]
        public DateTimeOffset? TimeSpentRetailing { get; set; }//For jquery UI with Local Offset
        [ColumnHeader("Total Time")]
        [TableField(ColumnName: "Total Time", NomenclatureRequirement = false)]
        public DateTimeOffset? TotalTime { get; set; }//For jquery UI with Local Offset
        [ColumnHeader("JW User")]
        [TableField(ColumnName: "JW User", NomenclatureRequirement = false)]
        public string JWUser { get; set; }
        [ColumnHeader("Assigned JW User")]
        [TableField(ColumnName: "Assigned JW User", NomenclatureRequirement = false)]
        public string AssignedJWUser { get; set; }
        [ColumnHeader("Joint Working Calls")]
        [TableField(ColumnName: "Joint Working Calls", NomenclatureRequirement = false)]
        public int JointWorkingCalls { get; set; }
        [DisplayName("Manager JW Calls")]
        [TableField(ColumnName: "Manager JW Calls", NomenclatureRequirement = false)]
        public string ManagerJointWorkingCalls { get; set; }
        [ColumnHeader("$$ESM$$ Id")]
        [TableField(ColumnName: "ESM Id", NomenclatureRequirement = false)]
        public long ESMId { get; set; }
        [ColumnHeader("$$ESM$$ Rank")]
        [TableField(ColumnName: "ESM Rank", NomenclatureRequirement = true)]
        public string ESMRank { get; set; }
        [ColumnHeader("Day Start Location")]
        [TableField(ColumnName: "Day Start Location", NomenclatureRequirement = false)]
        public string DayStartLocation { get; set; }
        [ColumnHeader("MTD Dispatch In Revenue")]
        [TableField(ColumnName: "MTD Dispatch In Revenue", NomenclatureRequirement = false)]
        public double MTDDispatchInRevenue { get; set; }
        [ColumnHeader("MTD Dispatch In Units")]
        [TableField(ColumnName: "MTD Dispatch In Units", NomenclatureRequirement = false)]
        public double MTDDispatchInUnits { get; set; }
        [ColumnHeader("MTD Dispatch In Std Units")]
        [TableField(ColumnName: "MTD Dispatch In Std Units", NomenclatureRequirement = false)]
        public double MTDDispatchInStdUnits { get; set; }
        [ColumnHeader("LMTD Dispatch In Revenue")]
        [TableField(ColumnName: "LMTD Dispatch In Revenue", NomenclatureRequirement = false)]
        public double LMTDDispatchInRevenue { get; set; }
        [ColumnHeader("LMTD Dispatch In Units")]
        [TableField(ColumnName: "LMTD Dispatch In Units", NomenclatureRequirement = false)]
        public double LMTDDispatchInUnits { get; set; }
        [ColumnHeader("LMTD Dispatch In StdUnits")]
        [TableField(ColumnName: "LMTD Dispatch In StdUnits", NomenclatureRequirement = false)]
        public double LMTDDispatchInStdUnits { get; set; }
        [ColumnHeader("Number Of Effective Calls")]
        [TableField(ColumnName: "Number Of Effective Calls", NomenclatureRequirement = false)]
        public int NumberOfEffectiveCalls { get; set; }
        [ColumnHeader("First Activity Time")]
        [TableField(ColumnName: "First Activity Time", NomenclatureRequirement = false)]
        public DateTimeOffset? FirstActivityTime { get; set; }
        [ColumnHeader("Last Activity Time")]
        [TableField(ColumnName: "Last Activity Time", NomenclatureRequirement = false)]
        public DateTimeOffset? LastActivityTime { get; set; }
        [ColumnHeader("Total Active Time")]
        [TableField(ColumnName: "Total Active Time", NomenclatureRequirement = false)]
        public DateTimeOffset? TotalActivityTime { get; set; }
        [ColumnHeader("First Other Activity Time")]
        [TableField(ColumnName: "First Other Activity Time", NomenclatureRequirement = false)]
        public DateTimeOffset? FirstOtherActivityTime { get; set; }
        [ColumnHeader("Last Other Activity Time")]
        [TableField(ColumnName: "Last Other Activity Time", NomenclatureRequirement = false)]
        public DateTimeOffset? LastOtherActivityTime { get; set; }
        [ColumnHeader("Productivity")]
        [TableField(ColumnName: "Productivity", NomenclatureRequirement = false)]
        public double Productivity { get; set; }
        [ColumnHeader("$$Distributor$$")]
        [TableField(ColumnName: "Distributor", NomenclatureRequirement = false)]
        public string DistributorName { get; set; }
        [ColumnHeader("Route Name")]
        [TableField(ColumnName: "Route Name", NomenclatureRequirement = false)]
        public string RouteName { get; set; }
        [ColumnHeader("Assigned Route Name")]
        [TableField(ColumnName: "Assigned Route Name", NomenclatureRequirement = false)]
        public string AssignedRouteName { get; set; }
        [ColumnHeader("No Of Other Activities")]
        [TableField(ColumnName: "No Of Other Activities", NomenclatureRequirement = false)]
        public double NoOfOtherActivities { get; set; }
        [ColumnHeader("Is First Call OVC")]
        [TableField(ColumnName: "Is First Call OVC", NomenclatureRequirement = false)]
        public bool IsFirstCallOVC { get; set; }
        [ColumnHeader("LMTD Booking In Revenue")]
        [TableField(ColumnName: "LMTD Booking In Revenue", NomenclatureRequirement = false)]
        public double LMTDBookingInRevenue { get; set; }
        [ColumnHeader("Net Value")]
        [TableField(ColumnName: "Net Value", NomenclatureRequirement = false)]
        public double NetValue { get; set; }
        [ColumnHeader("LMTD Booking In StdUnits")]
        [TableField(ColumnName: "LMTD Booking In StdUnits", NomenclatureRequirement = false)]
        public double LMTDBookingInStdUnits { get; set; }
        [ColumnHeader("LMTD Booking In Units")]
        [TableField(ColumnName: "LMTD Booking In Units", NomenclatureRequirement = false)]
        public double LMTDBookingInUnits { get; set; }
        [TableField(ColumnName: "User Count", NomenclatureRequirement = false)]
        public int? UserCount { get; set; }
        [ColumnHeader("Primary Category")]
        [TableField(ColumnName: "Primary Category", NomenclatureRequirement = false)]
        public string PrimaryCategory { get; set; }
        [ColumnHeader("User Length (KM)")]
        [TableField(ColumnName: "User Length (KM)", NomenclatureRequirement = false)]
        public double? UserLength { get; set; }
        [ColumnHeader("Beat Length (KM)")]
        [TableField(ColumnName: "Beat Length (KM)", NomenclatureRequirement = false)]
        public double? BeatLength { get; set; }
        [ColumnHeader("Selected Journey $$Outlet$$")]
        [TableField(ColumnName: "SelectedJourneyOutlets", NomenclatureRequirement = true)]
        public int? SelectedJourneyOutlets { get; set; }
    }

    public class EmployeeSummarySumarizedDTO
    {
        [TableField(ColumnName: "Id", NomenclatureRequirement = false)]
        public int Id { get; set; }
        //[ColumnHeader("$$GSM$$")]
        //[TableField(ColumnName: "GSM", NomenclatureRequirement = false)]
        //public string GSM { get; set; }
        //[ColumnHeader("$$NSM$$")]
        //[TableField(ColumnName: "NSM", NomenclatureRequirement = false)]
        //public string NSM { get; set; }
        //[ColumnHeader("$$ZSM$$")]
        //[TableField(ColumnName: "ZSM", NomenclatureRequirement = false)]
        //public string ZSM { get; set; }
        //[ColumnHeader("$$RSM$$")]
        //[TableField(ColumnName: "RSM", NomenclatureRequirement = false)]
        //public string RSM { get; set; }
        [ColumnHeader("Reporting Manager")]
        [TableField(ColumnName: "Reporting Manager", NomenclatureRequirement = false)]
        public string ReportingManager { get; set; }
        //[ColumnHeader("$$ASM$$")]
        //[TableField(ColumnName: "ASM", NomenclatureRequirement = false)]
        //public string ASM { get; set; }
        //[ColumnHeader("$$ESM$$ Name")]
        //[TableField(ColumnName: "ESM Name", NomenclatureRequirement = false)]
        //public string ESMName { get; set; }
        //[ColumnHeader("Day Start Date")]
        //[TableField(ColumnName: "Day Start Date", NomenclatureRequirement = false)]
        //public DateTimeOffset DayStartDate { get; set; }
        //[ColumnHeader("Day Start Time")]
        //[TableField(ColumnName: "Day Start Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? DayStartTime { get; set; }
        //[ColumnHeader("Day End Time")]
        //[TableField(ColumnName: "Day Start Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? DayEndTime { get; set; }
        //[ColumnHeader("Day End Type")]
        //[TableField(ColumnName: "Day End Type", NomenclatureRequirement = false)]
        //public DayEndType DayEndType { get; set; }
        //[ColumnHeader("First Call Time")]
        //[TableField(ColumnName: "First Call Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? FirstCallTime { get; set; }
        //[ColumnHeader("First Productive Call Time")]
        //[TableField(ColumnName: "First Productive Call Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? FirstProductiveCallTime { get; set; }
        [ColumnHeader("Retail Time in Minutes")]
        [TableField(ColumnName: "Retail Time in Minutes", NomenclatureRequirement = false)]
        public double TimeSpentRetailinginMinutes { get; set; }
        [TableField(ColumnName: "SC", NomenclatureRequirement = false)]
        public int SC { get; set; }
        [TableField(ColumnName: "TC", NomenclatureRequirement = false)]
        public int TC { get; set; }
        [TableField(ColumnName: "PC", NomenclatureRequirement = false)]
        public int PC { get; set; }
        [TableField(ColumnName: "OVC", NomenclatureRequirement = false)]
        public int OVC { get; set; }
        [ColumnHeader("Order Value")]
        [TableField(ColumnName: "Order Value", NomenclatureRequirement = false)]
        public double OrderBookingInRevenue { get; set; }
        [ColumnHeader("MTD Booking In Revenue")]
        [TableField(ColumnName: "MTD Booking In Revenue", NomenclatureRequirement = false)]
        public double MTDBookingInRevenue { get; set; }
        //[ColumnHeader("Selected Beat")]
        //[TableField(ColumnName: "Selected Beat", NomenclatureRequirement = false)]
        //public string SelectedBeat { get; set; }
        //[ColumnHeader("$$ESM$$ HQ")]
        //[TableField(ColumnName: "ESM HQ", NomenclatureRequirement = false)]
        //public string ESMHQ { get; set; }
        //[ColumnHeader("$$Region$$")]
        //[TableField(ColumnName: "Region", NomenclatureRequirement = false)]
        //public string Region { get; set; }
        //[ColumnHeader("$$Zone$$")]
        //[TableField(ColumnName: "Zone", NomenclatureRequirement = false)]
        //public string Zone { get; set; }
        //[ColumnHeader("$$ESM$$ Erp Id")]
        //[TableField(ColumnName: "ESM Erp Id", NomenclatureRequirement = false)]
        //public string ESM_ERP_ID { get; set; }
        //[ColumnHeader("$$ESM$$ Contact No")]
        //[TableField(ColumnName: "ESM Contact No", NomenclatureRequirement = false)]
        //public string ESMContactNo { get; set; }
        [TableField(ColumnName: "OVT", NomenclatureRequirement = false)]
        public int OVT { get; set; }
        [TableField(ColumnName: "CAP", NomenclatureRequirement = false)]
        public double CAP { get; set; }
        [ColumnHeader("Adherence (%)")]
        [TableField(ColumnName: "Adherence (%)", NomenclatureRequirement = false)]
        public double Adherence { get; set; }
        //[ColumnHeader("Last Call Time")]
        //[TableField(ColumnName: "Last Call Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? LastCallTime { get; set; }
        //[ColumnHeader("Last Productive Call Time")]
        //[TableField(ColumnName: "Last Productive Call Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? LastProductiveCallTime { get; set; }
        //[ColumnHeader("Total Time In Minutes")]
        //[TableField(ColumnName: "Total Time In Minutes", NomenclatureRequirement = false)]
        //public double TotalTimeInMinutes { get; set; }
        //[ColumnHeader("Is Late")]
        //[TableField(ColumnName: "Is Late", NomenclatureRequirement = false)]
        //public bool IsLate { get; set; }
        [ColumnHeader("Order Booking In Unit")]
        [TableField(ColumnName: "Order Booking In Unit", NomenclatureRequirement = false)]
        public double OrderBookingInUnit { get; set; }
        [ColumnHeader("Order Booking In StdUnit")]
        [TableField(ColumnName: "Order Booking In StdUnit", NomenclatureRequirement = false)]
        public double OrderBookingInStdUnit { get; set; }
        [ColumnHeader("MTD Booking in Unit")]
        [TableField(ColumnName: "MTD Booking in Unit", NomenclatureRequirement = false)]
        public double MTDBookinginUnit { get; set; }
        [ColumnHeader("MTD Booking In StandardUnit")]
        [TableField(ColumnName: "MTD Booking In StandardUnit", NomenclatureRequirement = false)]
        public double MTDBookingInStandardUnit { get; set; }
        [ColumnHeader("Total Scheme Discount")]
        [TableField(ColumnName: "Total Scheme Discount", NomenclatureRequirement = false)]
        public double TotalSchemeDiscount { get; set; }
        [ColumnHeader("Total SchemeQty")]
        [TableField(ColumnName: "Total SchemeQty", NomenclatureRequirement = false)]
        public double TotalSchemeQty { get; set; }
        [ColumnHeader("Total Product Wise Discount")]
        [TableField(ColumnName: "Total Product Wise Discount", NomenclatureRequirement = false)]
        public double TotalProductWiseDiscount { get; set; }
        [ColumnHeader("Lines Cut")]
        [TableField(ColumnName: "Lines Cut", NomenclatureRequirement = false)]
        public double LinesCut { get; set; }
        [ColumnHeader("No Of Styles")]
        [TableField(ColumnName: "No Of Styles", NomenclatureRequirement = false)]
        public int NoOfStyles { get; set; }
        [ColumnHeader("No Of Sec Category")]
        [TableField(ColumnName: "No Of Sec Category", NomenclatureRequirement = false)]
        public int NoOfSecCategory { get; set; }
        [ColumnHeader("Scheme Effective Calls")]
        [TableField(ColumnName: "Scheme Effective Calls", NomenclatureRequirement = false)]
        public int SchemeEffectiveCalls { get; set; }
        [ColumnHeader("TO")]
        [TableField(ColumnName: "TO", NomenclatureRequirement = false)]
        public int TelephonicOrders { get; set; }
        [ColumnHeader("New Outlets")]
        [TableField(ColumnName: "New Outlets", NomenclatureRequirement = false)]
        public int NewOutlets { get; set; }
        [ColumnHeader("New Outlets Sales in Unit")]
        [TableField(ColumnName: "New Outlets Sales in Unit", NomenclatureRequirement = false)]
        public double NewOutletsSalesinUnit { get; set; }
        [ColumnHeader("New Outlets Sales in StdUnit")]
        [TableField(ColumnName: "New Outlets Sales in StdUnit", NomenclatureRequirement = false)]
        public double NewOutletsSalesinStdUnit { get; set; }
        [ColumnHeader("New Outlets Revenue")]
        [TableField(ColumnName: "New Outlets Revenue", NomenclatureRequirement = false)]
        public double NewOutletsRevenue { get; set; }
        [TableField(ColumnName: "LPC", NomenclatureRequirement = false)]
        public double LPC { get; set; }
        [ColumnHeader("Styles Per call")]
        [TableField(ColumnName: "Styles Per call", NomenclatureRequirement = false)]
        public double StylesPercall { get; set; }
        [ColumnHeader("SC Per Call")]
        [TableField(ColumnName: "SC Per Call", NomenclatureRequirement = false)]
        public double SCPerCall { get; set; }
        //[ColumnHeader("Assigned Reason Category")]
        //[TableField(ColumnName: "Assigned Reason Category", NomenclatureRequirement = false)]
        //public string AssignedReasonCategory { get; set; }
        //[ColumnHeader("Assigned $$Beat$$")]
        //[TableField(ColumnName: "Assigned Beat", NomenclatureRequirement = false)]
        //public string AssignedBeat { get; set; }
        [ColumnHeader("Day Started")]
        [TableField(ColumnName: "Day Started", NomenclatureRequirement = false)]
        public bool DayStarted { get; set; }
        //[ColumnHeader("Day Start Type")]
        //[TableField(ColumnName: "Day Start Type", NomenclatureRequirement = false)]
        //public string DayStartType { get; set; }
        //[ColumnHeader("Day Start Type Enum")]
        //[TableField(ColumnName: "Day Start Type Enum", NomenclatureRequirement = false)]
        //public DayStartType DayStartTypeEnum { get; set; }
        //[TableField(ColumnName: "Reason", NomenclatureRequirement = false)]
        //public string Reason { get; set; }
        //[ColumnHeader("Reason Category")]
        //[TableField(ColumnName: "Reason Category", NomenclatureRequirement = false)]
        //public string ReasonCategory { get; set; }
        //[ColumnHeader("Time Spent Retailing")]
        //[TableField(ColumnName: "Time Spent Retailing", NomenclatureRequirement = false)]
        //public DateTimeOffset? TimeSpentRetailing { get; set; }//For jquery UI with Local Offset
        //[ColumnHeader("Total Time")]
        //[TableField(ColumnName: "Total Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? TotalTime { get; set; }//For jquery UI with Local Offset
        [ColumnHeader("JW User")]
        [TableField(ColumnName: "JW User", NomenclatureRequirement = false)]
        public string JWUser { get; set; }
        //[ColumnHeader("Assigned JW User")]
        //[TableField(ColumnName: "Assigned JW User", NomenclatureRequirement = false)]
        //public string AssignedJWUser { get; set; }
        //[ColumnHeader("Joint Working Calls")]
        //[TableField(ColumnName: "Joint Working Calls", NomenclatureRequirement = false)]
        //public int JointWorkingCalls { get; set; }
        [DisplayName("Manager JW Calls")]
        [TableField(ColumnName: "Manager JW Calls", NomenclatureRequirement = false)]
        public string ManagerJointWorkingCalls { get; set; }
        //[ColumnHeader("$$ESM$$ Id")]
        //[TableField(ColumnName: "ESM Id", NomenclatureRequirement = false)]
        //public long ESMId { get; set; }
        //[ColumnHeader("$$ESM$$ Rank")]
        //[TableField(ColumnName: "ESM Rank", NomenclatureRequirement = false)]
        //public string ESMRank { get; set; }
        //[ColumnHeader("Day Start Location")]
        //[TableField(ColumnName: "Day Start Location", NomenclatureRequirement = false)]
        //public string DayStartLocation { get; set; }
        [ColumnHeader("MTD Dispatch In Revenue")]
        [TableField(ColumnName: "MTD Dispatch In Revenue", NomenclatureRequirement = false)]
        public double MTDDispatchInRevenue { get; set; }
        [ColumnHeader("MTD Dispatch In Units")]
        [TableField(ColumnName: "MTD Dispatch In Units", NomenclatureRequirement = false)]
        public double MTDDispatchInUnits { get; set; }
        [ColumnHeader("MTD Dispatch In Std Units")]
        [TableField(ColumnName: "MTD Dispatch In Std Units", NomenclatureRequirement = false)]
        public double MTDDispatchInStdUnits { get; set; }
        [ColumnHeader("LMTD Dispatch In Revenue")]
        [TableField(ColumnName: "LMTD Dispatch In Revenue", NomenclatureRequirement = false)]
        public double LMTDDispatchInRevenue { get; set; }
        [ColumnHeader("LMTD Dispatch In Units")]
        [TableField(ColumnName: "LMTD Dispatch In Units", NomenclatureRequirement = false)]
        public double LMTDDispatchInUnits { get; set; }
        [ColumnHeader("LMTD Dispatch In StdUnits")]
        [TableField(ColumnName: "LMTD Dispatch In StdUnits", NomenclatureRequirement = false)]
        public double LMTDDispatchInStdUnits { get; set; }
        [ColumnHeader("Number Of Effective Calls")]
        [TableField(ColumnName: "Number Of Effective Calls", NomenclatureRequirement = false)]
        public int NumberOfEffectiveCalls { get; set; }
        //[ColumnHeader("First Activity Time")]
        //[TableField(ColumnName: "First Activity Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? FirstActivityTime { get; set; }
        //[ColumnHeader("Last Activity Time")]
        //[TableField(ColumnName: "Last Activity Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? LastActivityTime { get; set; }
        //[ColumnHeader("Total Active Time")]
        //[TableField(ColumnName: "Total Active Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? TotalActivityTime { get; set; }
        //[ColumnHeader("First Other Activity Time")]
        //[TableField(ColumnName: "First Other Activity Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? FirstOtherActivityTime { get; set; }
        //[ColumnHeader("Last Other Activity Time")]
        //[TableField(ColumnName: "Last Other Activity Time", NomenclatureRequirement = false)]
        //public DateTimeOffset? LastOtherActivityTime { get; set; }
        [ColumnHeader("Productivity")]
        [TableField(ColumnName: "Productivity", NomenclatureRequirement = false)]
        public double Productivity { get; set; }
        //[ColumnHeader("$$Distributor$$")]
        //[TableField(ColumnName: "Distributor", NomenclatureRequirement = false)]
        //public string DistributorName { get; set; }
        //[ColumnHeader("Route Name")]
        //[TableField(ColumnName: "Route Name", NomenclatureRequirement = false)]
        //public string RouteName { get; set; }
        //[ColumnHeader("Assigned Route Name")]
        //[TableField(ColumnName: "Assigned Route Name", NomenclatureRequirement = false)]
        //public string AssignedRouteName { get; set; }
        [ColumnHeader("No Of Other Activities")]
        [TableField(ColumnName: "No Of Other Activities", NomenclatureRequirement = false)]
        public double NoOfOtherActivities { get; set; }
        [ColumnHeader("Is First Call OVC")]
        [TableField(ColumnName: "Is First Call OVC", NomenclatureRequirement = false)]
        public bool IsFirstCallOVC { get; set; }
        [ColumnHeader("LMTD Booking In Revenue")]
        [TableField(ColumnName: "LMTD Booking In Revenue", NomenclatureRequirement = false)]
        public double LMTDBookingInRevenue { get; set; }
        [ColumnHeader("LMTD Booking In StdUnits")]
        [TableField(ColumnName: "LMTD Booking In StdUnits", NomenclatureRequirement = false)]
        public double LMTDBookingInStdUnits { get; set; }
        [ColumnHeader("LMTD Booking In Units")]
        [TableField(ColumnName: "LMTD Booking In Units", NomenclatureRequirement = false)]
        public double LMTDBookingInUnits { get; set; }
        //[TableField(ColumnName: "User Count", NomenclatureRequirement = false)]
        //public int? UserCount { get; set; }
        //[ColumnHeader("Primary Category")]
        //[TableField(ColumnName: "Primary Category", NomenclatureRequirement = false)]
        //public string PrimaryCategory { get; set; }


        [ColumnHeader("Reportee Name")]
        [TableField(ColumnName: "Reportee Name", NomenclatureRequirement = false)]
        public string ReporteeName { get; set; }
        [ColumnHeader("Net Value")]
        [TableField(ColumnName: "Net Value", NomenclatureRequirement = false)]
        public double NetValue { get; set; }
        [ColumnHeader("Selected Journey $$Outlet$$")]
        [TableField(ColumnName: "SelectedJourneyOutlets", NomenclatureRequirement = true)]
        public int? SelectedJourneyOutlets { get; set; }
    }
}
