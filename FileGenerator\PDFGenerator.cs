﻿using FileGenerator.Attributes;
using FileGenerator.Interfaces;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using iTextSharp.text;
using iTextSharp.text.pdf;
using FileGenerator.DataTableHelpers;
using System.Reflection;
using iTextSharp.text.pdf.draw;
using System.Collections;
using System.Text.RegularExpressions;
using System.Globalization;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;

namespace FileGenerator
{
    public class PDFGenerator
    {
        private readonly CompanyNomenclatureSpecifier nomenclatureSpecifier;
        private readonly bool _useNomenclature;

        public PDFGenerator(CompanyNomenclatureSpecifier nomenclatureSpecifier = null)
        {
            this.nomenclatureSpecifier = nomenclatureSpecifier;
            _useNomenclature = nomenclatureSpecifier != null;
        }
        public void MakePDF<T1>(long companyId, Stream output, string sheetName, IEnumerable<T1> data, IEnumerable<T1> dataForSecTable, int? noOfColumnsInInfoTable, Paragraph singnature = null, string GroupColumn = null, string ValueColumn = null, List<string> columnsToDelete = null, PdfPTable TotalTable = null)
        {        
            if (data == null || data.Count()==0)
            {
                    var document = new Document(PageSize.A2.Rotate(), 50, 50, 25, 25);
                    var writer = PdfWriter.GetInstance(document, output);
                    writer.CloseStream = false;
                    document.Open();
                    var message = new Paragraph("Day not started by User Or Preferences not set")
                    {
                        Alignment = Element.ALIGN_CENTER
                    };
                    document.Add(message);
                    document.Close();
            }
            else
            {
                PivotCreater pivotCreater = new PivotCreater();

                var columns = data.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
                var Table1 = CreateInfoTable(companyId, data, noOfColumnsInInfoTable, columnsToDelete);
                var document = new Document(PageSize.A2.Rotate(), 50, 50, 25, 25);
                var writer = PdfWriter.GetInstance(document, output);
                writer.CloseStream = false;
                document.Open();
                document.Add(AddCompanyLogo(companyId));
                //document.Add(new Paragraph(sheetName));
                //document.Add(AddFALogo());
                document.Add(Table1);
                if (dataForSecTable.Count() > 0)
                {
                    ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
                    var dt = ConvertToDataTable.GetDataTableFromList(dataForSecTable, sheetName);
                    if (GroupColumn != null)
                    {
                        dt = RemoveColumnsAndChangeHeader(companyId, columns, dt, columnsToDelete);
                        var pivotDt = pivotCreater.Pivot(dt, dt.Columns[GroupColumn], dt.Columns[ValueColumn]);
                        var pdfTableForPivto = CreateDataTable(pivotDt);
                        document.Add(pdfTableForPivto);
                    }
                    else
                    {
                        dt = RemoveColumns(companyId, columns, dt, columnsToDelete);
                        var pdfTableFlat = CreateDataTable(dt, columns, columnsToDelete);
                        document.Add(pdfTableFlat);
                    }
                    if (TotalTable != null)
                    {
                        document.Add(TotalTable);
                    }
                }
                else
                {
                    var nosale = new Paragraph("User have not booked any order")
                    {
                        Alignment = Element.ALIGN_CENTER,


                    };
                    document.Add(nosale);
                }
                if (singnature != null)
                {
                    singnature.Alignment = Element.ALIGN_LEFT;
                    document.Add(singnature);
                }
                var asmPara = new Paragraph()
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(asmPara);
                document.Close();
                //output.Close();
            }
            
        }
        public void MakeMultipleDataPDF<T1,T2>(long companyId, Stream output, string sheetName, IEnumerable<T1> data, IEnumerable<T2> dataForSecTable, int? noOfColumnsInInfoTable, Paragraph singnature = null, string GroupColumn = null, string ValueColumn = null, List<string> columnsToDelete = null, List<PdfPTable> TotalTable = null)
        {
            if (data == null || data.Count() == 0)
            {
                var document = new Document(PageSize.A2, 50, 50, 25, 25);
                var writer = PdfWriter.GetInstance(document, output);
                writer.CloseStream = false;
                document.Open();
                var message = new Paragraph("Day not started by User Or Preferences not set")
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(message);
                document.Close();
            }
            else
            {
                PivotCreater pivotCreater = new PivotCreater();
                var document = new Document(PageSize.A2, 50, 50, 25, 25);
                var writer = PdfWriter.GetInstance(document, output);
                writer.CloseStream = false;
                document.Open();
                foreach (var d in data)
                {
                    //var columns = d.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
                    //var columnsforsec = dataForSecTable.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
                    //var Table1 = CreateInfoTable(companyId, data, noOfColumnsInInfoTable, columnsToDelete);

                    document.Add(AddCompanyLogo(companyId));
                    ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
                    //var dt = ConvertToDataTable.GetDataTableFromList(data, sheetName);
                    //  dt = RemoveColumns(companyId, columns, dt, columnsToDelete);

                    //var dt = ConvertToDataTable.GetDataTableFromList(dataForSecTable, sheetName);
                    //dt = RemoveColumns(companyId, columnsforsec, dt, columnsToDelete);
                    var pdfTableFlat = CreateFlatDataTable(d,companyId, sheetName, dataForSecTable, noOfColumnsInInfoTable, columnsToDelete);
                            //document.Add(pdfTableFlat);
                            foreach(var pdf in pdfTableFlat)
                        {
                            document.Add(pdf);
                        }
                    document.Add(new Paragraph(" "));
                    LineSeparator line2 = new LineSeparator(1f, 100f, iTextSharp.text.BaseColor.Black, Element.ALIGN_LEFT, 1);
                    document.Add(line2);
                    document.NewPage();
                }
                document.Add(new Paragraph(" "));
                if (TotalTable != null && TotalTable.Count()>0)
                {
                    foreach (var pdf in TotalTable)
                    {
                        document.Add(pdf);
                    }
                }
                if (singnature != null)
                {
                    singnature.Alignment = Element.ALIGN_LEFT;
                    document.Add(singnature);
                }
                var asmPara = new Paragraph()
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(asmPara);
                document.Close();
                //output.Close();
            }

        }

        public void MakeMultipleDataPDFV2<T1, T2>(long companyId, string currency, Stream output, string sheetName, IEnumerable<T1> data, IEnumerable<T2> dataForSecTable, IEnumerable<T1> data2, IEnumerable<T2> dataForSecTable2, int? noOfColumnsInInfoTable, Dictionary<long, string> displayNameDictionary, Paragraph singnature = null, string GroupColumn = null, string ValueColumn = null, List<string> columnsToDelete = null, List<PdfPTable> TotalTable = null)
        {
            if (data == null || data.Count() == 0)
            {
                var document = new Document(PageSize.A4, 25f, 25f, 10f, 10f);
                var writer = PdfWriter.GetInstance(document, output);
                writer.CloseStream = false;
                document.Open();
                var message = new Paragraph("Day not started by User Or Preferences not set")
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(message);
                document.Close();
            }
            else
            {
                PivotCreater pivotCreater = new PivotCreater();
                //var document = new Document(PageSize.A4, 50, 50, 25, 25);
                Document document = new Document(PageSize.A4, 25f, 25f, 10f, 10f);
                var writer = PdfWriter.GetInstance(document, output);
                writer.CloseStream = false;
                document.Open();
                
                // make this generic
                var companyName = data.FirstOrDefault()?.GetType().GetProperty("CompanyName")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var companyAddress = data.FirstOrDefault()?.GetType().GetProperty("CompanyAddress")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var companyGSTIN = data.FirstOrDefault()?.GetType().GetProperty("CompanyGSTIN")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var distributorName = data.FirstOrDefault()?.GetType().GetProperty("DistributorName")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var distributorGSTIN = data.FirstOrDefault()?.GetType().GetProperty("DistributorGSTIN")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var distributorErpId = data.FirstOrDefault()?.GetType().GetProperty("DistributorERPId")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var distributorContactNo = data.FirstOrDefault()?.GetType().GetProperty("DistributorContactNo")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var distributorAddress = data.FirstOrDefault()?.GetType().GetProperty("DistributorAddress")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var distributorAttributeText1 = data.FirstOrDefault()?.GetType().GetProperty("DistributorAttributeText1")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var distributorAttributeText2 = data.FirstOrDefault()?.GetType().GetProperty("DistributorAttributeText2")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var distributorAttributeText3 = data.FirstOrDefault()?.GetType().GetProperty("DistributorAttributeText3")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var distributorAttributeNumber1 = data.FirstOrDefault()?.GetType().GetProperty("DistributorAttributeNumber1")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var distributorAttributeNumber2 = data.FirstOrDefault()?.GetType().GetProperty("DistributorAttributeNumber2")?.GetValue(data.FirstOrDefault())?.ToString() ?? "N/A";
                var dateProperty = data.FirstOrDefault()?.GetType().GetProperty("Date");
                string dateValue = dateProperty != null ? dateProperty.GetValue(data.FirstOrDefault())?.ToString() : "N/A";
                // till here

                DateTime parsedDate;
                if (!DateTime.TryParse(dateValue, out parsedDate))
                {
                    parsedDate = DateTime.Now;
                }
                string formattedDate = parsedDate.ToString("dd/MM/yyyy");

                var data2List = data2.ToList(); // consolidated page
                var dataList = data.ToList(); // individual pages

                for (int i = 0; i < data2List.Count; i++)
                {
                    var d = data2List[i];
                    //document.Add(CreateCompanyHeaderWithLogo(companyId, companyName, formattedDate, "CONSOLIDATED SUMMARY"));
                    document.Add(CreateCompanyHeaderWithLogoV2(companyId, companyName, companyAddress, companyGSTIN, formattedDate, "", "CONSOLIDATED SUMMARY", distributorName, distributorContactNo, distributorAddress, distributorGSTIN, distributorErpId, distributorAttributeText1, distributorAttributeText2, distributorAttributeText3, distributorAttributeNumber1, distributorAttributeNumber2, displayNameDictionary, columnsToDelete));
                    document.Add(new Paragraph(" "));
                    var pdfTableFlat = CreateSummaryTableV2(d, companyId, sheetName, dataForSecTable2, document, noOfColumnsInInfoTable, displayNameDictionary, columnsToDelete);
                    foreach (var pdf in pdfTableFlat)
                    {
                        document.Add(pdf);
                    }
                    CreateInfoDataTableV2(d, companyId, currency, sheetName, dataForSecTable, document, noOfColumnsInInfoTable, displayNameDictionary, columnsToDelete, false);
                    document.Add(new Paragraph(" "));
                    LineSeparator line2 = new LineSeparator(1f, 100f, BaseColor.Black, Element.ALIGN_LEFT, 1);
                    document.Add(line2);

                    document.NewPage();
                }
                for (int i = 0; i < dataList.Count; i++)
                {
                    var d = dataList[i];
                    var inoviceNo = d.GetType().GetProperty("InvoiceNumber")?.GetValue(d)?.ToString() ?? "N/A";
                    document.Add(CreateCompanyHeaderWithLogo(companyId, companyName, companyAddress, companyGSTIN, formattedDate, inoviceNo, "ORDER SUMMARY", displayNameDictionary, columnsToDelete));
                    document.Add(new Paragraph(" "));
                    var pdfTableFlat = CreateFlatDataTableV2(d, companyId, sheetName, dataForSecTable, document, noOfColumnsInInfoTable, displayNameDictionary, columnsToDelete);
                    foreach (var pdf in pdfTableFlat)
                    {
                        document.Add(pdf);
                    }
                    CreateInfoDataTableV2(d, companyId, currency, sheetName, dataForSecTable, document, noOfColumnsInInfoTable, displayNameDictionary, columnsToDelete);
                    document.Add(new Paragraph(" "));
                    LineSeparator line2 = new LineSeparator(1f, 100f, BaseColor.Black, Element.ALIGN_LEFT, 1);
                    document.Add(line2);

                    // Only add a new page if this is not the last order summary
                    if (i < dataList.Count - 1)
                    {
                        document.NewPage();
                    }
                }
                //foreach (var d in data2)
                //{
                //    document.Add(CreateCompanyHeaderWithLogo(companyId, companyName, formattedDate, "CONSOLIDATED SUMMARY"));
                //    document.Add(new Paragraph(" "));
                //    //ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
                //    var pdfTableFlat = CreateSummaryTableV2(d, companyId, sheetName, dataForSecTable2, document, noOfColumnsInInfoTable, columnsToDelete);
                //    foreach (var pdf in pdfTableFlat)
                //    {
                //        document.Add(pdf);
                //    }

                //    CreateInfoDataTableV2(d, companyId, currency, sheetName, dataForSecTable, document, noOfColumnsInInfoTable, columnsToDelete);
                //    document.Add(new Paragraph(" "));

                //    LineSeparator line2 = new LineSeparator(1f, 100f, iTextSharp.text.BaseColor.Black, Element.ALIGN_LEFT, 1);
                //    document.Add(line2);
                //    document.NewPage();
                //}
                //foreach (var d in data)
                //{
                //    document.Add(CreateCompanyHeaderWithLogo(companyId, companyName, formattedDate, "ORDER SUMMARY"));
                //    document.Add(new Paragraph(" "));
                //    //ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
                //    var pdfTableFlat = CreateFlatDataTableV2(d, companyId, sheetName, dataForSecTable, document, noOfColumnsInInfoTable, columnsToDelete);
                //    foreach (var pdf in pdfTableFlat)
                //    {
                //        document.Add(pdf);
                //    }

                //    CreateInfoDataTableV2(d, companyId,currency, sheetName, dataForSecTable, document, noOfColumnsInInfoTable, columnsToDelete);
                //    document.Add(new Paragraph(" "));

                //    LineSeparator line2 = new LineSeparator(1f, 100f, iTextSharp.text.BaseColor.Black, Element.ALIGN_LEFT, 1);
                //    document.Add(line2);
                //    document.NewPage();
                //}
                document.Add(new Paragraph(" "));
                if (TotalTable != null && TotalTable.Count() > 0)
                {
                    foreach (var pdf in TotalTable)
                    {
                        document.Add(pdf);
                    }
                }
                if (singnature != null)
                {
                    singnature.Alignment = Element.ALIGN_LEFT;
                    document.Add(singnature);
                }
                var asmPara = new Paragraph()
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(asmPara);
                document.Close();
                //output.Close();
            }

        }

        private PdfPTable CreateCompanyHeaderWithLogoV2(long companyId,string companyName, string companyAddress, string companyGSTIN, string date, string invoiceNumber, string headerName, string distributorName, string distributorContactNo, string distributorAddress, string distributorGSTIN, string distributorErpId, string distributorAttributeText1, string distributorAttributeText2, string distributorAttributeText3, string distributorAttributeNumber1, string distributorAttributeNumber2, Dictionary<long, string> displayNameDictionary,  List<string> columnsToDelete = null)
        {

            string userIconUrl = "https://abs.twimg.com/emoji/v2/72x72/1f464.png"; 
            iTextSharp.text.Image userIcon = iTextSharp.text.Image.GetInstance(new Uri(userIconUrl));
            userIcon.ScaleToFit(10f, 10f);

            // Create a header table with 3 columns.
            PdfPTable headerTable = new PdfPTable(3);
            headerTable.WidthPercentage = 100;
            headerTable.SetWidths(new float[] { 30, 40, 30 });

            // --- Left Cell: Company Logo & Details (Nested Table) ---
            PdfPTable companyTable = new PdfPTable(2);
            companyTable.WidthPercentage = 100;
            companyTable.SetWidths(new float[] { 20, 80 });

            // --- Company Logo ---
            PdfPCell logoCell = new PdfPCell
            {
                Border = PdfPCell.NO_BORDER,
                VerticalAlignment = Element.ALIGN_MIDDLE,
                HorizontalAlignment = Element.ALIGN_LEFT,
            };
            if (columnsToDelete == null || !columnsToDelete.Contains("CompanyLogo"))
            {
                logoCell.PaddingRight = 15f;
                logoCell.AddElement(AddCompanyLogo(companyId));   
            }
            companyTable.AddCell(logoCell);

            // --- Company Details ---
            PdfPCell textCell = new PdfPCell
            {
                Border = PdfPCell.NO_BORDER,
                VerticalAlignment = Element.ALIGN_MIDDLE,
                HorizontalAlignment = Element.ALIGN_LEFT,
                PaddingRight = 15f
            };
            if (columnsToDelete == null || !columnsToDelete.Contains("CompanyLogo"))
            {
                textCell.PaddingLeft = 35f;
            }
            else
            {
                textCell.PaddingLeft = 0f;
            }
            textCell.AddElement(new Paragraph(companyName, new Font(Font.HELVETICA, 9, Font.BOLD)));
            if (columnsToDelete == null || !columnsToDelete.Contains("Address"))
            {
                textCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(4) ? displayNameDictionary[4] : "Address" + ": " + companyAddress, new Font(Font.HELVETICA, 8)));
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("CompanyGSTIN"))
            {
                textCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(3) ? displayNameDictionary[3] : "CompanyGSTIN" + ": " + companyGSTIN, new Font(Font.HELVETICA, 8)));
            }
            //if (columnsToDelete == null || !columnsToDelete.Contains("CompanyTaxID"))
            //{
            //    textCell.AddElement(new Paragraph(displayNameDictionary[75] + ": " + companyGSTIN, new Font(Font.HELVETICA, 8)));
            //}
            if (columnsToDelete == null || !columnsToDelete.Contains("CompanyTaxID"))
            {
                textCell.AddElement(new Paragraph("Company TaxID: " + companyGSTIN, new Font(Font.HELVETICA, 8)));
            }
            companyTable.AddCell(textCell);

            // Wrap the companyTable into the left cell
            PdfPCell companyTableCell = new PdfPCell(companyTable);
            companyTableCell.Border = PdfPCell.NO_BORDER;
            companyTableCell.VerticalAlignment = Element.ALIGN_MIDDLE;
            headerTable.AddCell(companyTableCell);

            PdfPCell centerCell = new PdfPCell();
            centerCell.Border = PdfPCell.NO_BORDER;
            centerCell.HorizontalAlignment = Element.ALIGN_CENTER;
            centerCell.VerticalAlignment = Element.ALIGN_MIDDLE;
            centerCell.Padding = 0; 

            // Create a single phrase with a newline between the title and date.
            Phrase centerPhrase = new Phrase();
            centerPhrase.Add(new Chunk(headerName, new Font(Font.HELVETICA, 11, Font.BOLD)));
            centerPhrase.Add(Chunk.Newline);
            
            //if (columnsToDelete == null || !columnsToDelete.Contains("InvoiceNumber"))
            //{
            //    centerPhrase.Add(new Chunk("InvoiceNo: " + invoiceNumber, new Font(Font.HELVETICA, 9)));
            //}
            if (columnsToDelete == null || !columnsToDelete.Contains("Date"))
            {
                centerPhrase.Add(new Chunk(displayNameDictionary.ContainsKey(6) ? displayNameDictionary[6] : "Date" + ": " + date, new Font(Font.HELVETICA, 9)));
            }

            // Create a paragraph from the phrase (set centered alignment explicitly)
            Paragraph centerParagraph = new Paragraph(centerPhrase)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingBefore = 0,
                SpacingAfter = 0
            };

            centerCell.AddElement(centerParagraph);
            headerTable.AddCell(centerCell);


            // --- Row 1: Right Cell (Delivery Details) ---
            PdfPCell rightCell = new PdfPCell();
            rightCell.Border = PdfPCell.NO_BORDER;
            rightCell.HorizontalAlignment = Element.ALIGN_RIGHT;
            rightCell.VerticalAlignment = Element.ALIGN_MIDDLE;
            rightCell.Padding = 0;
            rightCell.PaddingLeft = 35f;
            rightCell.PaddingRight = 0f;

            // "PLACE OF DELIVERY:" text.
            rightCell.AddElement(new Paragraph("DISTRIBUTOR DETAILS:", new Font(Font.HELVETICA, 7))
            {
                SpacingBefore = 0,
                SpacingAfter = 1f
            });
            Font textFont = new Font(Font.HELVETICA, 8, Font.BOLD);
            Phrase outletIconPhrase = new Phrase();
            outletIconPhrase.Add(new Chunk(userIcon, 0, -1, true)); 
            outletIconPhrase.Add(new Chunk(" " + distributorName, textFont));
            rightCell.AddElement(new Paragraph(outletIconPhrase)
            {
                SpacingBefore = 1f,
                SpacingAfter = 1f
            });
            // Outlet contact.
            rightCell.AddElement(new Paragraph("(+91) " + distributorContactNo, new Font(Font.HELVETICA, 6))
            {
                SpacingBefore = 1f,
                SpacingAfter = 1f
            });
            if (columnsToDelete == null || !columnsToDelete.Contains("DistributorAddress"))
            {
                rightCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(9) ? displayNameDictionary[9] : "DistributorAddress" + ": " + distributorAddress, new Font(Font.HELVETICA, 6))
                {
                    SpacingBefore = 1f,
                    SpacingAfter = 0
                });
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("DistributorGSTIN"))
            {
                rightCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(10) ? displayNameDictionary[10]: "DistributorGSTIN" + ": " + distributorGSTIN, new Font(Font.HELVETICA, 6))
                {
                    SpacingBefore = 1f,
                    SpacingAfter = 0
                });
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("DistributorTaxId"))
            {
                rightCell.AddElement(new Paragraph("TaxID: " + distributorGSTIN, new Font(Font.HELVETICA, 6))
                {
                    SpacingBefore = 1f,
                    SpacingAfter = 0
                });
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("DistributorErpId"))
            {
                rightCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(39) ? displayNameDictionary[39] : "DistributorErpId" + ": " + distributorErpId, new Font(Font.HELVETICA, 6))
                {
                    SpacingBefore = 1f,
                    SpacingAfter = 0
                });
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("DistributorAttributeText1"))
            {
                rightCell.AddElement(new Paragraph(distributorAttributeText1, new Font(Font.HELVETICA, 6))
                {
                    SpacingBefore = 1f,
                    SpacingAfter = 0
                });
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("DistributorAttributeText2"))
            {
                rightCell.AddElement(new Paragraph(distributorAttributeText2, new Font(Font.HELVETICA, 6))
                {
                    SpacingBefore = 1f,
                    SpacingAfter = 0
                });
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("DistributorAttributeText3"))
            {
                rightCell.AddElement(new Paragraph(distributorAttributeText3, new Font(Font.HELVETICA, 6))
                {
                    SpacingBefore = 1f,
                    SpacingAfter = 0
                });
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("DistributorAttributeNumber1"))
            {
                rightCell.AddElement(new Paragraph(distributorAttributeNumber1, new Font(Font.HELVETICA, 6))
                {
                    SpacingBefore = 1f,
                    SpacingAfter = 0
                });
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("DistributorAttributeNumber2"))
            {
                rightCell.AddElement(new Paragraph(distributorAttributeNumber2, new Font(Font.HELVETICA, 6))
                {
                    SpacingBefore = 1f,
                    SpacingAfter = 0
                });
            }
            headerTable.AddCell(rightCell);

            return headerTable;
        }

        private PdfPTable CreateCompanyHeaderWithLogo(long companyId, string companyName, string companyAddress, string companyGSTIN, string date, string invoiceNumber, string headerName, Dictionary<long, string> displayNameDictionary, List<string> columnsToDelete = null)
        {
            PdfPTable headerTable = new PdfPTable(3); // Three columns for Left, Center, Right
            headerTable.WidthPercentage = 100;
            headerTable.SetWidths(new float[] { 30, 40, 30 }); // Adjust width ratio: 30% | 40% | 30%

            // **Part 1: Left Side - Logo & Company Details**
            PdfPTable companyTable = new PdfPTable(2); // Nested table for logo + details
            companyTable.SetWidths(new float[] { 20, 80 }); // 20% Logo, 80% Text
            companyTable.WidthPercentage = 100;

            // --- Company Logo ---
            PdfPCell logoCell = new PdfPCell
            {
                Border = PdfPCell.NO_BORDER,
                VerticalAlignment = Element.ALIGN_MIDDLE,
                HorizontalAlignment = Element.ALIGN_LEFT,
            };
            if (columnsToDelete == null || !columnsToDelete.Contains("CompanyLogo"))
            {
                logoCell.PaddingRight = 15f;
                logoCell.AddElement(AddCompanyLogo(companyId));
            }
            companyTable.AddCell(logoCell);

            // Company Details Cell
            PdfPCell textCell = new PdfPCell();
            textCell.Border = PdfPCell.NO_BORDER;
            textCell.VerticalAlignment = Element.ALIGN_MIDDLE;
            textCell.HorizontalAlignment = Element.ALIGN_LEFT;
            if (columnsToDelete == null || !columnsToDelete.Contains("CompanyLogo"))
            {
                textCell.PaddingLeft = 35f;
            } else
            {
                textCell.PaddingLeft = 0f;
            }
            
            textCell.AddElement(new Paragraph(companyName, new Font(Font.HELVETICA, 9, Font.BOLD)));
            if (columnsToDelete == null || !columnsToDelete.Contains("Address"))
            {
                textCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(4) ? displayNameDictionary[4] : "Address" + ": " + companyAddress, new Font(Font.HELVETICA, 8)));
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("CompanyGSTIN"))
            {
                textCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(3) ? displayNameDictionary[3] : "CompanyGSTIN" + ": " + companyGSTIN, new Font(Font.HELVETICA, 8)));
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("CompanyTaxID"))
            {
                textCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(75) ? displayNameDictionary[75] : "CompanyTaxID" + ": " + companyGSTIN, new Font(Font.HELVETICA, 8)));
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("CompanyTaxID"))
            {
                textCell.AddElement(new Paragraph("Company TaxID: " + companyGSTIN, new Font(Font.HELVETICA, 8)));
            }
            companyTable.AddCell(textCell);

            // Wrap the companyTable into the left cell
            PdfPCell companyTableCell = new PdfPCell(companyTable);
            companyTableCell.Border = PdfPCell.NO_BORDER;
            companyTableCell.VerticalAlignment = Element.ALIGN_MIDDLE;
            headerTable.AddCell(companyTableCell);

            // **Part 2: Center - Order Summary Title**
            PdfPCell titleCell = new PdfPCell(new Phrase(headerName, new Font(Font.HELVETICA, 11, Font.BOLD)));
            titleCell.HorizontalAlignment = Element.ALIGN_CENTER; // Center-align the title
            titleCell.VerticalAlignment = Element.ALIGN_MIDDLE;
            titleCell.Border = PdfPCell.NO_BORDER;
            titleCell.PaddingTop = 5f;
            headerTable.AddCell(titleCell);

            // **Part 3: Right Side - Date**
            if (columnsToDelete == null || !columnsToDelete.Contains("InvoiceNumber"))
            {
                PdfPCell dateCell = new PdfPCell(new Phrase(displayNameDictionary.ContainsKey(71) ? displayNameDictionary[71] : "Invoice Number" + ": " + invoiceNumber, new Font(Font.HELVETICA, 9)));
                dateCell.HorizontalAlignment = Element.ALIGN_RIGHT; // Right-align the date
                dateCell.VerticalAlignment = Element.ALIGN_MIDDLE;
                dateCell.Border = PdfPCell.NO_BORDER;
                dateCell.PaddingRight = 10f; // Space to avoid touching edge
                headerTable.AddCell(dateCell);
            }
            if (columnsToDelete == null || !columnsToDelete.Contains("Date"))
            {
                PdfPCell dateCell = new PdfPCell(new Phrase(displayNameDictionary.ContainsKey(6) ? displayNameDictionary[6] : "Date" + ": " + date, new Font(Font.HELVETICA, 9)));
                dateCell.HorizontalAlignment = Element.ALIGN_RIGHT; // Right-align the date
                dateCell.VerticalAlignment = Element.ALIGN_MIDDLE;
                dateCell.Border = PdfPCell.NO_BORDER;
                dateCell.PaddingRight = 10f; // Space to avoid touching edge
                headerTable.AddCell(dateCell);
            }

            return headerTable;


        }


        //private PdfPTable CreateCompanyHeaderWithLogo(long companyId, string companyName, string address, string gst, string helpline)
        //{
        //    PdfPTable headerTable = new PdfPTable(2);
        //    headerTable.WidthPercentage = 100;
        //    headerTable.SetWidths(new float[] { 10, 90 }); // Adjust column ratio

        //    // Add Company Logo
        //    PdfPCell logoCell = new PdfPCell();
        //    logoCell.Border = PdfPCell.NO_BORDER;
        //    logoCell.VerticalAlignment = Element.ALIGN_MIDDLE; // Align logo vertically
        //    logoCell.HorizontalAlignment = Element.ALIGN_LEFT;
        //    logoCell.PaddingRight = 0f; // Reduce space between logo and text
        //    logoCell.AddElement(AddCompanyLogo(companyId));
        //    headerTable.AddCell(logoCell);

        //    // Add Company Header Details
        //    PdfPCell textCell = new PdfPCell();
        //    textCell.Border = PdfPCell.NO_BORDER;
        //    textCell.VerticalAlignment = Element.ALIGN_MIDDLE; // Align text with the logo
        //    textCell.HorizontalAlignment = Element.ALIGN_LEFT;
        //    textCell.PaddingLeft = 0f; // Reduce gap between logo and text

        //    textCell.AddElement(new Paragraph(companyName, new Font(Font.HELVETICA, 9, Font.BOLD))); // Slightly increase font size
        //    textCell.AddElement(new Paragraph(address, new Font(Font.HELVETICA, 8)));
        //    textCell.AddElement(new Paragraph("GST: " + gst + " | Helpline: " + helpline, new Font(Font.HELVETICA, 8)));

        //    headerTable.AddCell(textCell);

        //    return headerTable;
        //}
        private IElement AddCompanyLogo(long companyId)
        {
            string companyLogoURL = "https://manage.fieldassist.in/FieldAssistPOC/Company/GetCompanyLogoViaId?companyId=" + companyId.ToString();

            iTextSharp.text.Image jpg = iTextSharp.text.Image.GetInstance(new Uri(companyLogoURL));
            //Resize image depend upon your need
           
            jpg.ScaleToFit(80f, 60f);

            //Give space before image

            //jpg.SpacingBefore = 10f;

            //Give some space after the image

            //jpg.SpacingAfter = 1f;

            jpg.Alignment = Element.ALIGN_LEFT;


            return jpg;
        }

        private IElement AddCompanyLogoV2(long companyId, PdfWriter writer)
        {
            // Build the URL for the company logo.
            string companyLogoURL = "https://debug.fieldassist.in/FieldAssistPOC/Company/GetCompanyLogoViaId?companyId=" + companyId.ToString();

            // Load the image from the URL.
            iTextSharp.text.Image jpg = iTextSharp.text.Image.GetInstance(new Uri(companyLogoURL));

            // Define the desired diameter for the circular logo.
            float desiredDiameter = 180f; // Adjust this value for a larger or smaller circle.

            // Scale the image to fit within a square of size desiredDiameter x desiredDiameter.
            jpg.ScaleToFit(desiredDiameter, desiredDiameter);

            // Create a template of fixed size (square) based on the desired diameter.
            PdfTemplate template = writer.DirectContent.CreateTemplate(desiredDiameter, desiredDiameter);

            // Set the circle's radius (half of the desired diameter).
            float radius = desiredDiameter / 2f;

            // Draw a circle centered in the template.
            template.Circle(radius, radius, radius);
            // Set the circle as the clipping path.
            template.Clip();
            template.NewPath(); // Finalize the clipping path.

            // Add the image to the template.
            // By using desiredDiameter for both width and height, the image fills the square.
            template.AddImage(jpg, desiredDiameter, 0, 0, desiredDiameter, 0, 0);

            // Create an Image from the template, which now shows a circular clipping of the logo.
            return iTextSharp.text.Image.GetInstance(template);
        }



        public iTextSharp.text.Image AddFALogo()
        {

            string faLogoURL = "https://i0.wp.com/www.fieldassist.in/wp-content/uploads/2016/11/logo_1-1.png";

            iTextSharp.text.Image jpg = iTextSharp.text.Image.GetInstance(faLogoURL);
            //Resize image depend upon your need

            jpg.ScaleToFit(140f, 120f);

            //Give space before image

            //jpg.SpacingBefore = 10f;

            //Give some space after the image

            jpg.SpacingAfter = 1f;

            jpg.Alignment = Element.ALIGN_RIGHT;

            return jpg;
        }
        public List<PdfPTable> CreateFlatDataTable<T,T1>(T data,long companyId, string sheetName,IEnumerable<T1> dataforsec , int? noOfColumnsInInfoTable, List<string> columnsToDelete = null)
        {
            try
            {
                List<PdfPTable> toret = new List<PdfPTable>();
                var columns = data.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);

                var dateProperty = data.GetType().GetProperty("Date");
                string dateValue = dateProperty != null ? dateProperty.GetValue(data)?.ToString() : "N/A";

                // Extracting Place of Delivery Details
                var outletName = data.GetType().GetProperty("LocationName")?.GetValue(data)?.ToString() ?? "N/A";
                var outletAddress = data.GetType().GetProperty("LocationAddress")?.GetValue(data)?.ToString() ?? "N/A";
                var outletContact = data.GetType().GetProperty("LocationNumber")?.GetValue(data)?.ToString() ?? "N/A";

                // Extracting User Details
                var userName = data.GetType().GetProperty("EmployeeName")?.GetValue(data)?.ToString() ?? "N/A";
                var orderTime = data.GetType().GetProperty("OrderTime")?.GetValue(data)?.ToString() ?? "N/A";
                var beat = data.GetType().GetProperty("BeatName")?.GetValue(data)?.ToString() ?? "N/A";

                // Create Date Table
                PdfPTable dateTable = new PdfPTable(1);
                dateTable.WidthPercentage = 100;
                PdfPCell dateCell = new PdfPCell(new Phrase("Date: " + dateValue, new Font(Font.HELVETICA, 10, Font.BOLD)))
                {
                    HorizontalAlignment = Element.ALIGN_RIGHT,
                    Border = PdfPCell.NO_BORDER,
                    VerticalAlignment = Element.ALIGN_TOP,
                    PaddingTop = -5,
                    PaddingBottom = 0,
                    PaddingRight = 10
                };
                dateTable.AddCell(dateCell);
                toret.Add(dateTable);

                // Create Place of Delivery Table
                //PdfPTable placeOfDeliveryTable = new PdfPTable(2);
                //placeOfDeliveryTable.WidthPercentage = 100;

                //placeOfDeliveryTable.AddCell(new PdfPCell(new Phrase("Place of Delivery:", new Font(Font.HELVETICA, 10, Font.BOLD))) { Border = PdfPCell.NO_BORDER });
                //placeOfDeliveryTable.AddCell(new PdfPCell(new Phrase(outletName)) { Border = PdfPCell.NO_BORDER });
                //placeOfDeliveryTable.AddCell(new PdfPCell(new Phrase("Address:", new Font(Font.HELVETICA, 10, Font.BOLD))) { Border = PdfPCell.NO_BORDER });
                //placeOfDeliveryTable.AddCell(new PdfPCell(new Phrase(outletAddress)) { Border = PdfPCell.NO_BORDER });
                //placeOfDeliveryTable.AddCell(new PdfPCell(new Phrase("Contact No:", new Font(Font.HELVETICA, 10, Font.BOLD))) { Border = PdfPCell.NO_BORDER });
                //placeOfDeliveryTable.AddCell(new PdfPCell(new Phrase(outletContact)) { Border = PdfPCell.NO_BORDER });
                //toret.Add(placeOfDeliveryTable);

                //// Create User Details Table
                //PdfPTable userDetailsTable = new PdfPTable(2);
                //userDetailsTable.WidthPercentage = 100;

                //userDetailsTable.AddCell(new PdfPCell(new Phrase("User Details:", new Font(Font.HELVETICA, 10, Font.BOLD))) { Border = PdfPCell.NO_BORDER });
                //userDetailsTable.AddCell(new PdfPCell(new Phrase(userName)) { Border = PdfPCell.NO_BORDER });
                //userDetailsTable.AddCell(new PdfPCell(new Phrase("Order Time:", new Font(Font.HELVETICA, 10, Font.BOLD))) { Border = PdfPCell.NO_BORDER });
                //userDetailsTable.AddCell(new PdfPCell(new Phrase(orderTime)) { Border = PdfPCell.NO_BORDER });
                //userDetailsTable.AddCell(new PdfPCell(new Phrase("Beat:", new Font(Font.HELVETICA, 10, Font.BOLD))) { Border = PdfPCell.NO_BORDER });
                //userDetailsTable.AddCell(new PdfPCell(new Phrase(beat)) { Border = PdfPCell.NO_BORDER });
                //toret.Add(userDetailsTable);

                PdfPTable outerTable = new PdfPTable(1);
                outerTable.WidthPercentage = 100;
                PdfPCell outerCell = new PdfPCell();
                outerCell.Border = PdfPCell.NO_BORDER;
                outerCell.Padding = 10;
                //outerCell.CellEvent = new EmbossedBorder();

                PdfPTable detailsTable = new PdfPTable(2);
                detailsTable.WidthPercentage = 100;
                detailsTable.SetWidths(new float[] { 50, 50 });

                PdfPCell deliveryCell = new PdfPCell();
                deliveryCell.Border = PdfPCell.NO_BORDER;
                deliveryCell.AddElement(new Paragraph("PLACE OF DELIVERY:", new Font(Font.HELVETICA, 8, Font.BOLD)));
                deliveryCell.AddElement(new Paragraph("🏢 " + outletName, new Font(Font.HELVETICA, 10, Font.BOLD)));
                deliveryCell.AddElement(new Paragraph("(+91) " + outletContact));
                deliveryCell.AddElement(new Paragraph("GST: " + outletAddress));

                PdfPCell userCell = new PdfPCell();
                userCell.Border = PdfPCell.NO_BORDER;
                userCell.AddElement(new Paragraph("USER DETAILS:", new Font(Font.HELVETICA, 8, Font.BOLD)));
                userCell.AddElement(new Paragraph("👤 " + userName, new Font(Font.HELVETICA, 10, Font.BOLD)));
                userCell.AddElement(new Paragraph("(+91) " + outletContact));
                userCell.AddElement(new Paragraph("Beat: " + beat));

                detailsTable.AddCell(deliveryCell);
                detailsTable.AddCell(userCell);

                outerCell.AddElement(detailsTable);
                outerTable.AddCell(outerCell);

                toret.Add(outerTable);

                var item = data;
                foreach (var colProp in columns)
                {
                    if (columnsToDelete == null || !columnsToDelete.Contains(colProp.Name))
                    {
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (Tattr is TableFieldAttribute excelAttrOfT)
                        {
                            var Tcolumnname = excelAttrOfT.ColumnName;
                            var dataValue = colProp.GetValue(item) ?? "";
                            if (excelAttrOfT.PDFColCategory == PDFColCategory.Info && (dataValue.ToString() != "" || excelAttrOfT.ColumnRequirement == Requirement.Required))
                            {
                            
                                    PdfPTable table = new PdfPTable(noOfColumnsInInfoTable ?? 12)
                                    {
                                        WidthPercentage = 30,
                                        HorizontalAlignment = Element.ALIGN_LEFT,
                                    };

                                    if (_useNomenclature & excelAttrOfT.NomenclatureRequirement)
                                    {
                                        var listforheader = excelAttrOfT.ColumnName.Split(' ').ToList();
                                        var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                                        listforheader.RemoveAt(0);
                                        var striingotherthennomenclature = String.Join(" ", listforheader);
                                        Tcolumnname = nomenclaturename + " " + striingotherthennomenclature;
                                    }

                                    PdfPCell headerCell = new PdfPCell(new Phrase(excelAttrOfT.ColumnName))
                                    {
                                        HorizontalAlignment = Element.ALIGN_LEFT,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,
                                        BorderWidth = 0
                                    };
                                    table.AddCell(headerCell);

                                    PdfPCell dataCell = new PdfPCell(new Phrase(dataValue.ToString()))
                                    {
                                        HorizontalAlignment = Element.ALIGN_LEFT,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,
                                        BorderWidth = 0
                                    };
                                    table.AddCell(dataCell);

                                    toret.Add(table);
                                
                            }
                            else if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                            {
                                ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
                                if (dataValue.ToString() != "")
                                {
                                    var result = (IEnumerable<T1>)dataValue;
                                    var columnsforsec = result.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
                                    var dt = ConvertToDataTable.GetDataTableFromList(result, sheetName);
                                    dt = RemoveColumns(companyId, columnsforsec, dt, columnsToDelete);
                                    var pdfTableFlat = CreateDataTable(dt, columnsforsec, columnsToDelete);
                                    toret.Add(pdfTableFlat);
                                }
                            }
                        }
                    }


                }
                
                return toret;
            }
            catch (Exception ex) 
            { 
                return null; }

        }

        public List<PdfPTable> CreateFlatDataTableV2<T, T1>(T data, long companyId, string sheetName, IEnumerable<T1> dataforsec, Document document, int? noOfColumnsInInfoTable, Dictionary<long, string> displayNameDictionary, List<string> columnsToDelete = null)
        {
            try
            {
                List<PdfPTable> toret = new List<PdfPTable>();
                var columns = data.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);

                Font textFont = new Font(Font.HELVETICA, 10, Font.BOLD);

                string companyImageUrl = "https://abs.twimg.com/emoji/v2/72x72/1f3e2.png"; // URL for `🏢`
                iTextSharp.text.Image companyIcon = iTextSharp.text.Image.GetInstance(new Uri(companyImageUrl));
                companyIcon.ScaleToFit(10f, 10f);


                string UserimageUrl = "https://abs.twimg.com/emoji/v2/72x72/1f464.png"; //  URL for `👤`
                iTextSharp.text.Image userIconImage = iTextSharp.text.Image.GetInstance(new Uri(UserimageUrl));
                userIconImage.ScaleToFit(10f, 10f);

                // Extracting Place of Delivery Details
                var outletName = data.GetType().GetProperty("LocationName")?.GetValue(data)?.ToString() ?? "N/A";
                var GSTIN = data.GetType().GetProperty("GSTIN")?.GetValue(data)?.ToString() ?? "N/A";
                var outletContact = data.GetType().GetProperty("LocationNumber")?.GetValue(data)?.ToString() ?? "N/A";
                var outletAddress= data.GetType().GetProperty("LocationAddress")?.GetValue(data)?.ToString() ?? "N/A";
                var outletErpId= data.GetType().GetProperty("RetailerErpId")?.GetValue(data)?.ToString() ?? "N/A";
                var attributeText1 = data.GetType().GetProperty("AttributeText1")?.GetValue(data)?.ToString() ?? "N/A";
                var attributeText2 = data.GetType().GetProperty("AttributeText2")?.GetValue(data)?.ToString() ?? "N/A";
                var attributeText3 = data.GetType().GetProperty("AttributeText3")?.GetValue(data)?.ToString() ?? "N/A";
                var attributeNumber1 = data.GetType().GetProperty("AttributeNumber1")?.GetValue(data)?.ToString() ?? "N/A";
                var attributeNumber2 = data.GetType().GetProperty("AttributeNumber2")?.GetValue(data)?.ToString() ?? "N/A";
                var attributeNumber3 = data.GetType().GetProperty("AttributeNumber3")?.GetValue(data)?.ToString() ?? "N/A";

                // Extracting User Details
                var userName = data.GetType().GetProperty("EmployeeName")?.GetValue(data)?.ToString() ?? "N/A";
                var designation = data.GetType().GetProperty("Designation")?.GetValue(data)?.ToString() ?? "";
                var EmployeeContactNo = data.GetType().GetProperty("EmployeeContactNo")?.GetValue(data)?.ToString() ?? "N/A";
                var EmployeeERPId = data.GetType().GetProperty("EmployeeErpId")?.GetValue(data)?.ToString() ?? "N/A";
                var beat = data.GetType().GetProperty("BeatName")?.GetValue(data)?.ToString() ?? "N/A";
                var vanRegistrationNo = data.GetType().GetProperty("VanRegistrationNumber")?.GetValue(data)?.ToString() ?? "N/A";
                var vanErpId = data.GetType().GetProperty("VanName")?.GetValue(data)?.ToString() ?? "N/A";
                var userdetailsHeader = string.IsNullOrEmpty(designation) ? "USER DETAILS:" : designation;
                var employeeAttributeText1 = data.GetType().GetProperty("EmployeeAttributeText1")?.GetValue(data)?.ToString() ?? "N/A";
                var employeeAttributeText2 = data.GetType().GetProperty("EmployeeAttributeText2")?.GetValue(data)?.ToString() ?? "N/A";
                var employeeAttributeNumber1 = data.GetType().GetProperty("EmployeeAttributeNumber1")?.GetValue(data)?.ToString() ?? "N/A";
                var employeeAttributeNumber2 = data.GetType().GetProperty("EmployeeAttributeNumber2")?.GetValue(data)?.ToString() ?? "N/A";

                PdfPTable outerTable = new PdfPTable(1);
                outerTable.WidthPercentage = 100;
                outerTable.SetTotalWidth(new float[] { document.PageSize.Width - document.LeftMargin - document.RightMargin });
                outerTable.LockedWidth = true;

                PdfPCell outerCell = new PdfPCell();
                outerCell.Border = PdfPCell.NO_BORDER;
                outerCell.Padding = 10;
                outerCell.CellEvent = new EmbossedBorderV2(document);


                PdfPTable detailsTable = new PdfPTable(2);
                detailsTable.WidthPercentage = 100;
                detailsTable.SetWidths(new float[] { 50, 50 });

                PdfPCell deliveryCell = new PdfPCell();
                deliveryCell.Border = PdfPCell.NO_BORDER;
                deliveryCell.AddElement(new Paragraph("PLACE OF DELIVERY:", new Font(Font.HELVETICA, 8)));
                Phrase outletIconPhrase = new Phrase
                {
                    new Chunk(companyIcon, 0, 0, true),
                    new Chunk(" " + outletName, textFont)
                };
                deliveryCell.AddElement(new Paragraph(outletIconPhrase));
                deliveryCell.AddElement(new Paragraph("(+91) " + outletContact, new Font(Font.HELVETICA, 7)));
                if (columnsToDelete == null || !columnsToDelete.Contains("RetailerGSTIN"))
                {
                    deliveryCell.AddElement(new Paragraph((displayNameDictionary.ContainsKey(14) ? displayNameDictionary[14] : "GSTIN") + ": " + GSTIN, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("RetailerTaxID"))
                {
                    deliveryCell.AddElement(new Paragraph("TaxID: " + GSTIN, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("RetailerAddress"))
                {
                    deliveryCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(13) ? displayNameDictionary[13] : "RetailerAddress" + ": " + outletAddress, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("RetailerErpID"))
                {
                    deliveryCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(55) ? displayNameDictionary[55] : "RetailerErpID" + ": " + outletErpId, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("AttributeText1"))
                {
                    deliveryCell.AddElement(new Paragraph(attributeText1, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("AttributeText2"))
                {
                    deliveryCell.AddElement(new Paragraph(attributeText2, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("AttributeText3"))
                {
                    deliveryCell.AddElement(new Paragraph(attributeText3, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("AttributeNumber1"))
                {
                    deliveryCell.AddElement(new Paragraph(attributeNumber1, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("AttributeNumber2"))
                {
                    deliveryCell.AddElement(new Paragraph(attributeNumber2, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("AttributeNumber3"))
                {
                    deliveryCell.AddElement(new Paragraph(attributeNumber3, new Font(Font.HELVETICA, 7)));
                }
                PdfPCell userCell = new PdfPCell();
                userCell.Border = PdfPCell.NO_BORDER;
                if (columnsToDelete == null || !columnsToDelete.Contains("EmployeeName") || !columnsToDelete.Contains("EmployeeContactNo") || !columnsToDelete.Contains("BeatName") || (!columnsToDelete.Contains("UserErpID")))
                {
                    userCell.AddElement(new Paragraph(userdetailsHeader, new Font(Font.HELVETICA, 8)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("EmployeeName"))
                {
                     Phrase userIconPhrase = new Phrase
                    {
                        new Chunk(userIconImage, 0, 0, true),
                        new Chunk(" " + userName, textFont)
                    };
                    userCell.AddElement(new Paragraph(userIconPhrase));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("UserNumber"))
                {
                    userCell.AddElement(new Paragraph("(+91) " + EmployeeContactNo, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("UserErpID"))
                {
                    userCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(45) ? displayNameDictionary[45] : "UserErpID" + ": " + EmployeeERPId, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("BeatDetails"))
                {
                    userCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(17) ? displayNameDictionary[17] : "BeatDetails" + ": " + beat, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("VehicleNumber"))
                {
                    userCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(46) ? displayNameDictionary[46] : "VehicleNumber" + ": " + vanRegistrationNo, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("VehicleErpID"))
                {
                    userCell.AddElement(new Paragraph(displayNameDictionary.ContainsKey(49) ? displayNameDictionary[49] : "VehicleErpID" + ": " + vanErpId, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("EmployeeAttributeText1"))
                {
                    userCell.AddElement(new Paragraph(employeeAttributeText1, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("EmployeeAttributeText2"))
                {
                    userCell.AddElement(new Paragraph(employeeAttributeText2, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("EmployeeAttributeNumber1"))
                {
                    userCell.AddElement(new Paragraph(employeeAttributeNumber1, new Font(Font.HELVETICA, 7)));
                }
                if (columnsToDelete == null || !columnsToDelete.Contains("EmployeeAttributeNumber2"))
                {
                    userCell.AddElement(new Paragraph(employeeAttributeNumber2, new Font(Font.HELVETICA, 7)));
                }

                detailsTable.AddCell(deliveryCell);
                detailsTable.AddCell(userCell);

                outerCell.AddElement(detailsTable);
                outerTable.AddCell(outerCell);

                toret.Add(outerTable);

                // Add Summary Title
                //PdfPTable summaryTable = new PdfPTable(1);
                //summaryTable.WidthPercentage = 100f;
                //summaryTable.SpacingBefore = 10f;
                //PdfPCell summaryCell = new PdfPCell(new Phrase("SUMMARY", new Font(Font.HELVETICA, 10)))
                //{
                //    HorizontalAlignment = Element.ALIGN_LEFT,
                //    Padding = 5,
                //    Border = PdfPCell.NO_BORDER,
                //    BackgroundColor = BaseColor.White
                //};
                //summaryTable.AddCell(summaryCell);

                //toret.Add(summaryTable);

                var item = data;
                foreach (var colProp in columns)
                {
                    if (columnsToDelete == null || !columnsToDelete.Contains(colProp.Name))
                    {
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (Tattr is TableFieldAttribute excelAttrOfT)
                        {
                            var Tcolumnname = excelAttrOfT.ColumnName;
                            var dataValue = colProp.GetValue(item) ?? "";
                            if (excelAttrOfT.PDFColCategory == PDFColCategory.Info && (dataValue.ToString() != "" || excelAttrOfT.ColumnRequirement == Requirement.Required))
                            {
                            }
                            else if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                            {
                                ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
                                if (dataValue.ToString() != "")
                                {
                                    if (columnsToDelete == null)
                                    {
                                        columnsToDelete = new List<string>();
                                        columnsToDelete.AddRange(new List<string> { "Closetoexp", "Unit", "StockQty", "UnitPrice" });
                                    }
                                    else
                                    {
                                        columnsToDelete.AddRange(new List<string> { "Closetoexp",  "Unit", "StockQty", "UnitPrice" });
                                    }
                                    var result = (IEnumerable<T1>)dataValue;
                                    var columnsforsec = result.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
                                    var dt = ConvertToDataTable.GetDataTableFromList(result, sheetName);
                                    dt = RemoveColumns(companyId, columnsforsec, dt, columnsToDelete);
                                    var pdfTableFlat = CreateDataTableV2(dt, columnsforsec,document, displayNameDictionary, columnsToDelete);
                                    toret.Add(pdfTableFlat);
                                }
                            }
                        }
                    }


                }

                return toret;
            }
            catch (Exception ex)
            {
                return null;
            }

        }

        private string GetFormattedDecimal(object data, string propertyName)
        {
            object value = data.GetType().GetProperty(propertyName)?.GetValue(data);

            var indianCulture = new CultureInfo("en-IN");
            if (value == null) return "0.00";

            if (value is decimal decimalValue)
                return decimalValue.ToString("N2", indianCulture); 

            if (value is string stringValue && decimal.TryParse(stringValue, out decimal parsedDecimal))
                return parsedDecimal.ToString("N2", indianCulture); 

            return "0.00"; 
        }

        private string GetFormattedDecimalV2(object data, string propertyName)
        {
            object value = data.GetType().GetProperty(propertyName)?.GetValue(data);

            var indianCulture = new CultureInfo("en-IN");
            if (value == null) return "0.00";

            if (value is decimal decimalValue)
                return decimalValue.ToString("N2", indianCulture);

            if (value is string stringValue)
            {
                // Remove ₹ symbol and commas
                stringValue = stringValue.Replace("₹", "").Replace(",", "").Trim();

                if (decimal.TryParse(stringValue, out decimal parsedDecimal))
                    return parsedDecimal.ToString("N2", indianCulture);
            }

            return "0.00";
        }

        public List<PdfPTable> CreateInfoDataTableV2<T, T1>(T data, long companyId, string currency, string sheetName, IEnumerable<T1> dataforsec, Document document, int? noOfColumnsInInfoTable, Dictionary<long, string> displayNameDictionary, List<string> columnsToDelete = null, bool showRemarks = true)
        {
            try
            {
                List<PdfPTable> toret = new List<PdfPTable>();
                var columns = data.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);

                var indianCulture = new CultureInfo("en-IN");

                // Extracting Place of Delivery Details
                var RetailersIGST = GetFormattedDecimalV2(data, "RetailersIGST");
                var RetailersSGST = GetFormattedDecimalV2(data, "RetailersSGST");
                var RetailersCGST = GetFormattedDecimalV2(data, "RetailersCGST");
                var RetailersOrderValue = GetFormattedDecimal(data, "RetailersOrderValue");
                var TotalTax = GetFormattedDecimalV2(data, "TotalTax");
                var FOCBenefit = GetFormattedDecimal(data, "TotalFOCBenefit");
                var SchemeDiscount = GetFormattedDecimal(data, "RetailersSchemeDiscount");
                var TotalCESS = GetFormattedDecimal(data, "RetailersCESS");
                var Remarks = data.GetType().GetProperty("Remark")?.GetValue(data)?.ToString() ?? "N/A";

                //var TotalAmount = ((decimal.TryParse(RetailersOrderValue, out decimal amount) ? amount : 0) +
                //    (decimal.TryParse(RetailersSGST, out decimal sgst) ? sgst : 0) + (decimal.TryParse(RetailersIGST, out decimal igst) ? igst : 0) +
                //    (decimal.TryParse(RetailersCGST, out decimal cgst) ? cgst : 0) /*+ (decimal.TryParse(TotalCESS, out decimal cess) ? cess : 0)*/
                //    - (decimal.TryParse(SchemeDiscount, out decimal disc) ? disc : 0)).ToString("N2", indianCulture);

                var TotalBenefit = ((decimal.TryParse(FOCBenefit, out decimal focbenefit) ? focbenefit : 0) +
                    (decimal.TryParse(SchemeDiscount, out decimal schemediscount) ? schemediscount : 0));

                var payableAmount = ((decimal.TryParse(RetailersOrderValue, out decimal amount) ? amount : 0) + (decimal.TryParse(RetailersSGST, out decimal sgst) ? sgst : 0) + (decimal.TryParse(RetailersIGST, out decimal igst) ? igst : 0) +
                    (decimal.TryParse(RetailersCGST, out decimal cgst) ? cgst : 0) + (decimal.TryParse(TotalTax, out decimal tax) ? tax : 0) + (decimal.TryParse(TotalCESS, out decimal cess) ? cess : 0)).ToString("N2", indianCulture);

                string fontPath1 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts", "NotoSans-Regular.ttf");
                if (!File.Exists(fontPath1))
                {
                    throw new FileNotFoundException("Font files are missing!");
                }
                BaseFont baseFont1 = BaseFont.CreateFont(fontPath1, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);

                // Creating Summary Table
                PdfPTable summaryTable = new PdfPTable(2)
                {
                    WidthPercentage = 100f,
                    SpacingBefore = 5f
                };
                summaryTable.SetWidths(new float[] { 3f, 2f });

                summaryTable.AddCell(new PdfPCell(new Phrase("SUMMARY", new Font(Font.HELVETICA, 10)))
                {
                    Colspan = 2,
                    Border = PdfPCell.NO_BORDER,
                    PaddingBottom = 10
                });

                PdfPTable leftTable = new PdfPTable(2)
                {
                    WidthPercentage = 100f
                };
                leftTable.SetWidths(new float[] { 3f, 2f });

                void AddSummaryRow(string label, string value, bool IsGreen, int fontsize)
                {
                    var customGreen = new BaseColor(0x56, 0xA4, 0x59);
                    var ValueFont = IsGreen ? new Font(baseFont1, fontsize, Font.NORMAL, customGreen) : new Font(baseFont1, fontsize);
                    leftTable.AddCell(new PdfPCell(new Phrase(label, new Font(Font.HELVETICA, fontsize, Font.BOLD)))
                    {
                        Border = PdfPCell.NO_BORDER,
                        Padding = 5,
                        HorizontalAlignment = Element.ALIGN_LEFT
                    });
                    leftTable.AddCell(new PdfPCell(new Phrase(value, ValueFont))
                    {
                        Border = PdfPCell.NO_BORDER,
                        Padding = 5,
                        HorizontalAlignment = Element.ALIGN_RIGHT
                    });
                }

                void AddSummaryRowIfNotDeleted(string key, string label, string value, bool IsGreen, int fontsize)
                {
                    if (columnsToDelete == null || !columnsToDelete.Contains(key))
                    {
                        AddSummaryRow(label, value, IsGreen, fontsize);
                    }
                }

                AddSummaryRowIfNotDeleted("RetailersOrderValue", displayNameDictionary.ContainsKey(67) ? displayNameDictionary[67] : "Outlet Order Value", currency + RetailersOrderValue.ToString(), false, 9);
                AddSummaryRowIfNotDeleted("RetailersCGST", displayNameDictionary.ContainsKey(31) ? displayNameDictionary[31] : "Outlet CGST Value", currency + RetailersCGST.ToString(), false, 9);
                AddSummaryRowIfNotDeleted("RetailersSGST", displayNameDictionary.ContainsKey(32) ? displayNameDictionary[32] : "Outlet SGST Value", currency + RetailersSGST.ToString(), false, 9);
                AddSummaryRowIfNotDeleted("RetailersIGST", displayNameDictionary.ContainsKey(33) ? displayNameDictionary[33] : "Outlet IGST Value", currency + RetailersIGST.ToString(), false, 9);
                AddSummaryRowIfNotDeleted("TotalTax", displayNameDictionary.ContainsKey(30) ? displayNameDictionary[30] : "Total Tax Value", currency + TotalTax.ToString(), false, 9);
                AddSummaryRowIfNotDeleted("RetailersSchemeDiscount", displayNameDictionary.ContainsKey(34) ? displayNameDictionary[34] : "Outlet Scheme Discount" + " (-)", currency + SchemeDiscount, true, 9);
                AddSummaryRowIfNotDeleted("TotalFOCBenefit", displayNameDictionary.ContainsKey(69) ? displayNameDictionary[69] : "Total FOC Benefit", currency + FOCBenefit, true, 9);
                AddSummaryRowIfNotDeleted("TotalBenefits", displayNameDictionary.ContainsKey(70) ? displayNameDictionary[70] : "Total Discount", currency + TotalBenefit, true, 9);
                AddSummaryRowIfNotDeleted("RetailersCESS", displayNameDictionary.ContainsKey(68) ? displayNameDictionary[68] : "CESS", currency + TotalCESS.ToString(), false, 9);
                AddSummaryRowIfNotDeleted("RetailersNetOrderValue", displayNameDictionary.ContainsKey(35) ? displayNameDictionary[35] : "Net Amount", currency + payableAmount, false, 11);
                if(showRemarks)
                {
                    AddSummaryRowIfNotDeleted("Remark", displayNameDictionary.ContainsKey(36) ? displayNameDictionary[36] : "Remarks", Remarks, false, 9);
                }

                PdfPTable rightTable = new PdfPTable(1)
                {
                    WidthPercentage = 100f
                };

                rightTable.AddCell(new PdfPCell(new Phrase(displayNameDictionary.ContainsKey(29) ? displayNameDictionary[29] : "TOTAL AMOUNT", new Font(Font.HELVETICA, 10)))
                {
                    Border = PdfPCell.NO_BORDER,
                    Padding = 5,
                    HorizontalAlignment = Element.ALIGN_RIGHT
                });
                rightTable.AddCell(new PdfPCell(new Phrase(currency + payableAmount, new Font(baseFont1, 25, Font.BOLD)))
                {
                    Border = PdfPCell.NO_BORDER,
                    Padding = 5,
                    HorizontalAlignment = Element.ALIGN_RIGHT
                });

                PdfPCell leftCell = new PdfPCell(leftTable)
                {
                    Border = PdfPCell.NO_BORDER
                };
                PdfPCell rightCell = new PdfPCell(rightTable)
                {
                    Border = PdfPCell.NO_BORDER,
                    HorizontalAlignment = Element.ALIGN_RIGHT
                };

                summaryTable.AddCell(leftCell);
                summaryTable.AddCell(rightCell);

                PdfPTable outerSummaryTable = new PdfPTable(1)
                {
                    WidthPercentage = 100f,
                    SpacingBefore = 10f,
                    SpacingAfter = 12.5f
                };

                PdfPCell outerCell = new PdfPCell(summaryTable)
                {
                    Border = PdfPCell.NO_BORDER,
                    Padding = 10,
                    CellEvent = new EmbossedBorderV2(document)
                };
                outerSummaryTable.AddCell(outerCell);

                document.Add(outerSummaryTable);
                toret.Add(outerSummaryTable);
                return toret;
            }
            catch (Exception ex)
            {
                return null;
            }

        }

        public List<PdfPTable> CreateSummaryTableV2<T, T1>(T data, long companyId, string sheetName, IEnumerable<T1> dataforsec, Document document, int? noOfColumnsInInfoTable, Dictionary<long, string> displayNameDictionary, List<string> columnsToDelete = null)
        {
            try
            {
                List<PdfPTable> toret = new List<PdfPTable>();
                var columns = data.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);

                var item = data;
                foreach (var colProp in columns)
                {
                    if (columnsToDelete == null || !columnsToDelete.Contains(colProp.Name))
                    {
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (Tattr is TableFieldAttribute excelAttrOfT)
                        {
                            var Tcolumnname = excelAttrOfT.ColumnName;
                            var dataValue = colProp.GetValue(item) ?? "";
                            if (excelAttrOfT.PDFColCategory == PDFColCategory.Info && (dataValue.ToString() != "" || excelAttrOfT.ColumnRequirement == Requirement.Required))
                            {
                            }
                            else if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                            {
                                ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
                                if (dataValue.ToString() != "")
                                {
                                    if (columnsToDelete == null)
                                    {
                                        columnsToDelete = new List<string>();
                                        columnsToDelete.AddRange(new List<string> { "Closetoexp",  "Unit",  "StockQty", "UnitPrice" });
                                    }
                                    else
                                    {
                                        columnsToDelete.AddRange(new List<string> { "Closetoexp",  "Unit",  "StockQty", "UnitPrice" });
                                    }
                                    var result = (IEnumerable<T1>)dataValue;
                                    var columnsforsec = result.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
                                    var dt = ConvertToDataTable.GetDataTableFromList(result, sheetName);
                                    dt = RemoveColumns(companyId, columnsforsec, dt, columnsToDelete);
                                    var pdfTableFlat = CreateDataTableV2(dt, columnsforsec, document, displayNameDictionary, columnsToDelete);
                                    toret.Add(pdfTableFlat);
                                }
                            }
                        }
                    }


                }

                return toret;
            }
            catch (Exception ex)
            {
                return null;
            }

        }

        public class EmbossedBorderV3 : IPdfPCellEvent
        {
            private Document _document;
            public EmbossedBorderV3(Document document)
            {
                _document = document;
            }
            public void CellLayout(PdfPCell cell, Rectangle position, PdfContentByte[] canvases)
            {
                PdfContentByte canvas = canvases[PdfPTable.BACKGROUNDCANVAS];
                float left = _document.LeftMargin - 5;
                float right = _document.PageSize.Width - _document.RightMargin + 5;
                canvas.SetLineWidth(0.5f);
                canvas.SetColorStroke(new BaseColor(220, 220, 220)); // Light Grey Border

                // Remove the SetGrayFill call that was making everything white

                // Just draw the stroke without filling
                canvas.RoundRectangle(left, position.Bottom - 1, right - left, position.Height - 2, 8);
                canvas.Stroke(); // Only stroke, don't fill

                // Inner highlight effect
                //canvas.SetColorStroke(new BaseColor(200, 200, 200)); // Lighter Border for Raised Look
                //canvas.RoundRectangle(left + 1, position.Bottom - 2, right - left - 2, position.Height - 4, 8);
                canvas.Stroke();
            }
        }

        public class EmbossedBorderV2 : IPdfPCellEvent
        {
            private Document _document;

            public EmbossedBorderV2(Document document)
            {
                _document = document;
            }

            public void CellLayout(PdfPCell cell, Rectangle position, PdfContentByte[] canvases)
            {
                PdfContentByte canvas = canvases[PdfPTable.BACKGROUNDCANVAS];
                float left = _document.LeftMargin - 5;
                float right = _document.PageSize.Width - _document.RightMargin + 5;

                canvas.SetLineWidth(0.5f);
                canvas.SetColorStroke(new BaseColor(220, 220, 220)); // Light Grey Border
                canvas.SetGrayFill(1.0f); // Transparent Background

                // Outer Shadow Effect for Embossed Look
                //canvas.SetColorFill(new BaseColor(245, 245, 245)); // Slightly Darker for Shadow
                canvas.RoundRectangle(left, position.Bottom - 1, right - left, position.Height - 2, 8);
                canvas.FillStroke();

                //// Inner Highlight Effect
                //canvas.SetColorStroke(new BaseColor(200, 200, 200)); // Lighter Border for Raised Look
                //canvas.RoundRectangle(left + 1, position.Bottom - 2, right - left - 2, position.Height - 4, 8);
                canvas.Stroke();
            }
        }

        public PdfPTable CreateInfoTable<T>(long companyId, IEnumerable<T> data, int? noOfColumnsInInfoTable, List<string> columnsToDelete = null)
        {
            var columns = data.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            var firstrowofdata = data.Take(1);
            PdfPTable table = new PdfPTable(noOfColumnsInInfoTable ?? 12)
            {
                SpacingBefore = 10f,
                SpacingAfter = 12.5f
            };
            foreach (var item in firstrowofdata)
            {
                foreach (var colProp in columns)
                {
                    if (columnsToDelete == null || !columnsToDelete.Contains(colProp.Name))
                    {
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (Tattr is TableFieldAttribute excelAttrOfT)
                        {
                            var Tcolumnname = excelAttrOfT.ColumnName;
                            var dataValue = colProp.GetValue(item) ?? "";
                            if (excelAttrOfT.PDFColCategory == PDFColCategory.Info)
                            {
                                if (_useNomenclature & excelAttrOfT.NomenclatureRequirement)
                                {

                                    var listforheader = excelAttrOfT.ColumnName.Split(' ').ToList();
                                    var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                                    var updatelist = listforheader.Remove(listforheader[0]);
                                    var striingotherthennomenclature = String.Join(" ", listforheader);
                                    Tcolumnname = nomenclaturename + " " + striingotherthennomenclature;


                                    PdfPCell headerCell = new PdfPCell(new Phrase(excelAttrOfT.ColumnName))
                                    {
                                        BackgroundColor = new iTextSharp.text.BaseColor(211, 211, 211),
                                        HorizontalAlignment = Element.ALIGN_CENTER,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,

                                    };

                                    table.AddCell(headerCell);

                                    PdfPCell dataCell = new PdfPCell(new Phrase(dataValue.ToString()))
                                    {
                                        BackgroundColor = new iTextSharp.text.BaseColor(255, 255, 255),
                                        HorizontalAlignment = Element.ALIGN_CENTER,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,
                                    };
                                    table.AddCell(dataCell);
                                }

                                else
                                {


                                    PdfPCell headerCell = new PdfPCell(new Phrase(excelAttrOfT.ColumnName))
                                    {
                                        BackgroundColor = new iTextSharp.text.BaseColor(211, 211, 211),
                                        HorizontalAlignment = Element.ALIGN_CENTER,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,
                                    };
                                    table.AddCell(headerCell);

                                    PdfPCell dataCell = new PdfPCell(new Phrase(dataValue.ToString()))
                                    {
                                        BackgroundColor = new iTextSharp.text.BaseColor(255, 255, 255),
                                        HorizontalAlignment = Element.ALIGN_CENTER,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,
                                    };
                                    table.AddCell(dataCell);

                                }
                            }
                        }
                    }


                }
            }

            return table;

        }

        public PdfPTable CreateDataTable(DataTable data)
        {


            PdfPTable table = new PdfPTable(data.Columns.Count);
            table.DefaultCell.HorizontalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.VerticalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.Padding = 3;
            table.SpacingBefore = 10f;
            table.SpacingAfter = 12.5f;
            table.WidthPercentage = 100f;
            foreach (DataColumn col in data.Columns)
            {
                table.AddCell(col.ColumnName);
            }
            for (var i = 0; i < data.Rows.Count; i++)
            {
                for (var j = 0; j < data.Columns.Count; j++)
                {
                    table.AddCell(data.Rows[i].ItemArray[j].ToString());
                }

            }

            return table;

        }
        public PdfPTable CreateDataTable(DataTable data, IEnumerable<PropertyInfo> columns, List<string> columnsToDelete = null)
        {
            PdfPTable table = new PdfPTable(data.Columns.Count);
            table.DefaultCell.HorizontalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.VerticalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.Padding = 3;
            table.SpacingBefore = 10f;
            table.SpacingAfter = 12.5f;
            table.WidthPercentage = 100f;
            foreach (DataColumn col in data.Columns)
            {
                if (!columnsToDelete.Contains(col.ColumnName))
                {
                    var colProp = columns.ToList().Where(d => d.Name == col.ColumnName).Select(d => d).FirstOrDefault();
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT)
                    {
                        if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                        {
                            table.AddCell(excelAttrOfT.ColumnName);
                        }
                    }
                }
            }
            for (var i = 0; i < data.Rows.Count; i++)
            {
                for (var j = 0; j < data.Columns.Count; j++)
                {
                    if (!columnsToDelete.Contains(data.Columns[j].ColumnName))
                    {
                        var colProp = columns.ToList().Where(d => d.Name == data.Columns[j].ColumnName).Select(d => d).FirstOrDefault();
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (Tattr is TableFieldAttribute excelAttrOfT)
                        {
                            if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                            {
                                var Tcolumnname = excelAttrOfT.ColumnName;
                                if (excelAttrOfT.ColumnDataType == DataType.Date)
                                {
                                    var dataValue = ((DateTimeOffset)data.Rows[i].ItemArray[j]).ToString("dd/MM/yyyy");
                                    table.AddCell(dataValue);
                                }
                                else if (excelAttrOfT.ColumnDataType == DataType.Time)
                                {
                                    var dataValue = data.Rows[i].ItemArray[j] != DBNull.Value ? ((DateTimeOffset)data.Rows[i].ItemArray[j]).ToString(@"HH: mm"):"";
                                    table.AddCell(dataValue);
                                }
                                else
                                {
                                    var dataValue = data.Rows[i].ItemArray[j].ToString();
                                    table.AddCell(dataValue);
                                }

                            }
                        }
                    }
                }

            }

            return table;
        }

        public PdfPTable CreateDataTableV2(DataTable data, IEnumerable<PropertyInfo> columns, Document document, Dictionary<long, string> displayNameDictionary, List<string> columnsToDelete = null)
        {
            if (columnsToDelete == null)
            {
                columnsToDelete = new List<string>();
            }

            foreach (DataColumn col in data.Columns)
            {
                // If not already marked for deletion.
                if (!columnsToDelete.Contains(col.ColumnName))
                {
                    // Find the matching property for this column
                    var colProp = columns.FirstOrDefault(d => d.Name == col.ColumnName);
                    if (colProp != null)
                    {
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute)) as TableFieldAttribute;
                        if (Tattr != null && Tattr.ColumnRequirement == Requirement.HideIfNull)
                        {
                            bool allNull = true;
                            foreach (DataRow row in data.Rows)
                            {
                                // Check if the value is not DBNull and not an empty string.
                                if (row[col] != DBNull.Value && !string.IsNullOrWhiteSpace(row[col].ToString()))
                                {
                                    allNull = false;
                                    break;
                                }
                            }
                            if (allNull)
                            {
                                columnsToDelete.Add(col.ColumnName);
                            }
                        }
                    }
                }
            }

            var visibleColumns = data.Columns.Cast<DataColumn>().Where(col => !columnsToDelete.Contains(col.ColumnName)).ToList();
            int visibleColumnCount = visibleColumns.Count;

            PdfPTable table = new PdfPTable(visibleColumnCount)
            {
                WidthPercentage = 100f,
                SpacingBefore = 10f,
                SpacingAfter = 12.5f,
                KeepTogether = false
            };

            float[] columnWidths = new float[visibleColumnCount];
            columnWidths[0] = 2.5f; // Increase width for the first column
            columnWidths[visibleColumnCount - 1] = 2f;
            for (int i = 1; i < visibleColumnCount - 1; i++)
            {
                columnWidths[i] = 1.5f;
            }
            table.SetWidths(columnWidths);

            int colCount = visibleColumnCount;
            int firstColIndex = 0;
            int lastColIndex = colCount - 1;

            string fontPath1 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts", "NotoSans-Regular.ttf");
            if (!File.Exists(fontPath1))
            {
                throw new FileNotFoundException("Font files are missing!");
            }
            BaseFont baseFont1 = BaseFont.CreateFont(fontPath1, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);

            int colIndex = 0;
            foreach (DataColumn col in visibleColumns)
            {
                if (!columnsToDelete.Contains(col.ColumnName))
                {
                    var colProp = columns.FirstOrDefault(d => d.Name == col.ColumnName);
                    if (colProp != null)
                    {
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute)) as TableFieldAttribute;

                        var nomenclaturename = String.Empty;
                        if (_useNomenclature && Tattr != null && Tattr.NomenclatureRequirement)
                        {
                            var header = Tattr.ColumnName;
                            nomenclaturename = nomenclatureSpecifier.GetHeaderName(header);
                            if (header == "SuperUnit")
                            {
                                nomenclaturename = nomenclaturename != header ? "Qty(" + nomenclaturename + ")" : "Qty(SU)";
                            }
                        }
                        if (col.ColumnName == "ProductName")
                        {   
                            nomenclaturename = displayNameDictionary.ContainsKey(18) ? displayNameDictionary[18]: Tattr.ColumnName;
                        }
                        if(col.ColumnName == "ErpCode")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(19) ? displayNameDictionary[19] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "PTR")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(23) ? displayNameDictionary[23] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "Quantity")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(21) ? displayNameDictionary[21] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "HSNCode")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(20) ? displayNameDictionary[20] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "MRP" && nomenclaturename == Tattr.ColumnName)
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(22) ? displayNameDictionary[22] : Tattr.ColumnName;
                        }
                        //if (col.ColumnName == "UnitPrice")
                        //{
                        //    nomenclaturename = displayNameDictionary.ContainsKey(23) ? displayNameDictionary[23] : Tattr.ColumnName;
                        //}
                        if (col.ColumnName == "GST")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(26) ? displayNameDictionary[26] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "GSTAmount")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(27) ? displayNameDictionary[27] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "CESS")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(28) ? displayNameDictionary[28] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "FOCValue")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(63) ? displayNameDictionary[63] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "Discount")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(25) ? displayNameDictionary[25] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "ReturnQty")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(62) ? displayNameDictionary[62] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "FOCQty")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(24) ? displayNameDictionary[24] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "FOCValue")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(63) ? displayNameDictionary[63] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "QuantityInSuperUnit")
                        {
                            nomenclaturename = displayNameDictionary.ContainsKey(37) ? displayNameDictionary[37] : Tattr.ColumnName;
                        }
                        if (col.ColumnName == "Value")
                        {
                            nomenclaturename = "TOTAL";
                        }

                        if (Tattr != null && Tattr.PDFColCategory == PDFColCategory.Table)
                        {
                            Font columnFont = new Font(Font.HELVETICA, 8);

                            var displayHeaderName = !String.IsNullOrEmpty(nomenclaturename) ? nomenclaturename : Tattr.ColumnName;
                            PdfPCell headerCell = new PdfPCell(new Phrase(displayHeaderName, columnFont))
                            {
                                BackgroundColor = BaseColor.White, // Transparent Background
                                HorizontalAlignment = Element.ALIGN_LEFT,
                                Padding = 10,
                                Border = PdfPCell.NO_BORDER
                            };
                            table.AddCell(headerCell);
                        }
                    }
                }
                colIndex++;
            }

            table.HeaderRows = 1; // Keeps header on top

            // Adding Data Rows with Alternate Row Coloring
            int rowIndex = 0;
            foreach (DataRow row in data.Rows)
            {
                colIndex = 0;
                foreach (DataColumn col in visibleColumns)
                {
                    if (!columnsToDelete.Contains(col.ColumnName))
                    {
                        var colProp = columns.FirstOrDefault(d => d.Name == col.ColumnName);
                        if (colProp != null)
                        {
                            var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute)) as TableFieldAttribute;
                            if (Tattr != null && Tattr.PDFColCategory == PDFColCategory.Table)
                            {
                                string cellValue = row[col] != DBNull.Value ? row[col].ToString() : "";
                                cellValue = Regex.Replace(cellValue, "^\\d+\\. ", "");

                                if (Tattr.ColumnDataType == DataType.Date)
                                {
                                    cellValue = ((DateTimeOffset)row[col]).ToString("dd/MM/yyyy");
                                }
                                else if (Tattr.ColumnDataType == DataType.Time)
                                {
                                    cellValue = ((DateTimeOffset)row[col]).ToString("HH:mm");
                                }

                                Font cellFont;

                                if (Tattr.ColumnDataType == DataType.Currency)
                                {
                                    cellFont = new Font(baseFont1, 9);
                                } else
                                {
                                    cellFont = new Font(Font.HELVETICA, 8, (colIndex == firstColIndex) ? Font.BOLD : Font.NORMAL);
                                }

                                //Font cellFont = (colIndex == lastColIndex)
                                //                ? new Font(baseFont1, 9)
                                //                : new Font(Font.HELVETICA, 8, (colIndex == firstColIndex) ? Font.BOLD : Font.NORMAL);


                                PdfPCell dataCell = new PdfPCell(new Phrase(cellValue, cellFont))
                                {
                                    HorizontalAlignment = Element.ALIGN_LEFT,
                                    Padding = 10,
                                    Border = PdfPCell.NO_BORDER,
                                    BackgroundColor = (rowIndex % 2 == 0) ? new BaseColor(240, 240, 240) : BaseColor.White
                            };
                                table.AddCell(dataCell);
                            }
                        }
                    }
                    colIndex++;
                }
                rowIndex++;
            }


            table.TableEvent = new TableBorderEvent(document);

                return table;
           
            //PdfPTable outerTable = new PdfPTable(1)
            //{
            //    WidthPercentage = 100f,
            //    SpacingBefore = 10f,
            //    SpacingAfter = 12.5f,
            //    KeepTogether = false
            //};

            //PdfPCell outerCell = new PdfPCell(table)
            //{
            //    Border = PdfPCell.NO_BORDER,
            //    Padding = 10,
            //    CellEvent = new EmbossedBorderV3(document)
            //};
            //outerTable.AddCell(outerCell);
            //return outerTable;

        }

        public class TableBorderEvent : IPdfPTableEvent
        {
            private Document _document;
            public TableBorderEvent(Document document)
            {
                _document = document;
            }

            public void TableLayout(PdfPTable table, float[][] widths, float[] heights, int headerRows, int rowStart, PdfContentByte[] canvases)
            {
                PdfContentByte canvas = canvases[PdfPTable.BACKGROUNDCANVAS];
                float left = widths[0][0];
                float right = widths[0][widths[0].Length - 1];
                float top = heights[0];
                float bottom = heights[heights.Length - 1];

                canvas.SetLineWidth(0.5f);
                canvas.SetColorStroke(new BaseColor(220, 220, 220));
                canvas.RoundRectangle(left - 5, bottom - 1, (right - left) + 10, (top - bottom) + 2, 8);
                canvas.Stroke();
            }
        }


        public PdfPTable CreateDataTableV3(DataTable data, IEnumerable<PropertyInfo> columns, Document document, List<string> columnsToDelete = null)
        {
            if (columnsToDelete == null)
            {
                columnsToDelete = new List<string>();
            }

            // First pass: Identify columns to delete based on HideIfNull attribute
            foreach (DataColumn col in data.Columns)
            {
                // If not already marked for deletion
                if (!columnsToDelete.Contains(col.ColumnName))
                {
                    // Find the matching property for this column
                    var colProp = columns.FirstOrDefault(d => d.Name == col.ColumnName);
                    if (colProp != null)
                    {
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute)) as TableFieldAttribute;
                        if (Tattr != null && Tattr.ColumnRequirement == Requirement.HideIfNull)
                        {
                            bool allNull = true;
                            foreach (DataRow row in data.Rows)
                            {
                                // Check if the value is not DBNull and not an empty string
                                if (row[col] != DBNull.Value && !string.IsNullOrWhiteSpace(row[col].ToString()))
                                {
                                    allNull = false;
                                    break;
                                }
                            }
                            if (allNull)
                            {
                                columnsToDelete.Add(col.ColumnName);
                            }
                        }
                    }
                }
            }

            // Get only visible columns (those not marked for deletion)
            var visibleColumns = data.Columns.Cast<DataColumn>()
                .Where(col => !columnsToDelete.Contains(col.ColumnName))
                .ToList();

            int visibleColumnCount = visibleColumns.Count;

            // Create table with correct number of columns
            PdfPTable table = new PdfPTable(visibleColumnCount)
            {
                WidthPercentage = 100f,
                SpacingBefore = 10f,
                SpacingAfter = 12.5f,
                KeepTogether = true
            };

            // Set column widths
            if (visibleColumnCount > 0)
            {
                float[] columnWidths = new float[visibleColumnCount];
                columnWidths[0] = 2.5f; // Increase width for the first column

                if (visibleColumnCount > 1)
                    columnWidths[visibleColumnCount - 1] = 2f;

                for (int i = 1; i < visibleColumnCount - 1; i++)
                {
                    columnWidths[i] = 1.5f;
                }
                table.SetWidths(columnWidths);
            }

            int firstColIndex = 0;
            int lastColIndex = visibleColumnCount - 1;

            // Setup font
            string fontPath1 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts", "NotoSans-Regular.ttf");
            if (!File.Exists(fontPath1))
            {
                throw new FileNotFoundException("Font files are missing!");
            }
            BaseFont baseFont1 = BaseFont.CreateFont(fontPath1, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);

            // Add header cells for visible columns only
            int colIndex = 0;
            foreach (DataColumn col in visibleColumns)
            {
                var colProp = columns.FirstOrDefault(d => d.Name == col.ColumnName);
                if (colProp != null)
                {
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute)) as TableFieldAttribute;
                    if (Tattr != null && Tattr.PDFColCategory == PDFColCategory.Table)
                    {
                        Font columnFont = new Font(Font.HELVETICA, 8);

                        PdfPCell headerCell = new PdfPCell(new Phrase(Tattr.ColumnName, columnFont))
                        {
                            BackgroundColor = BaseColor.White, // Transparent Background
                            HorizontalAlignment = Element.ALIGN_LEFT,
                            Padding = 10,
                            Border = PdfPCell.NO_BORDER
                        };
                        table.AddCell(headerCell);
                    }
                }
                colIndex++;
            }

            table.HeaderRows = 1; // Keeps header on top

            // Adding Data Rows with Alternate Row Coloring - only for visible columns
            int rowIndex = 0;
            foreach (DataRow row in data.Rows)
            {
                colIndex = 0;
                foreach (DataColumn col in visibleColumns)
                {
                    var colProp = columns.FirstOrDefault(d => d.Name == col.ColumnName);
                    if (colProp != null)
                    {
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute)) as TableFieldAttribute;
                        if (Tattr != null && Tattr.PDFColCategory == PDFColCategory.Table)
                        {
                            string cellValue = row[col] != DBNull.Value ? row[col].ToString() : "";
                            cellValue = Regex.Replace(cellValue, "^\\d+\\. ", "");

                            if (Tattr.ColumnDataType == DataType.Date)
                            {
                                cellValue = ((DateTimeOffset)row[col]).ToString("dd/MM/yyyy");
                            }
                            else if (Tattr.ColumnDataType == DataType.Time)
                            {
                                cellValue = ((DateTimeOffset)row[col]).ToString("HH:mm");
                            }

                            Font cellFont = (colIndex == lastColIndex)
                                            ? new Font(baseFont1, 9)
                                            : new Font(Font.HELVETICA, 8, (colIndex == firstColIndex) ? Font.BOLD : Font.NORMAL);

                            PdfPCell dataCell = new PdfPCell(new Phrase(cellValue, cellFont))
                            {
                                HorizontalAlignment = Element.ALIGN_LEFT,
                                Padding = 10,
                                Border = PdfPCell.NO_BORDER,
                                BackgroundColor = (rowIndex % 2 == 0) ? new BaseColor(240, 240, 240) : BaseColor.White
                            };
                            table.AddCell(dataCell);
                        }
                    }
                    colIndex++;
                }
                rowIndex++;
            }

            PdfPTable outerTable = new PdfPTable(1)
            {
                WidthPercentage = 100f,
                SpacingBefore = 10f,
                SpacingAfter = 12.5f
            };

            PdfPCell outerCell = new PdfPCell(table)
            {
                Border = PdfPCell.NO_BORDER,
                Padding = 10,
                CellEvent = new EmbossedBorderV3(document)
            };
            outerTable.AddCell(outerCell);
            return outerTable;
        }

        public PdfPTable CreateDataTableWithoutHeader(DataTable data, IEnumerable<PropertyInfo> columns, List<string> columnsToDelete = null)
        {
            PdfPTable table = new PdfPTable(data.Columns.Count);
            table.DefaultCell.HorizontalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.VerticalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.Padding = 3;
            table.SpacingBefore = 10f;
            table.SpacingAfter = 12.5f;
            table.WidthPercentage = 100f;
            for (var i = 0; i < data.Rows.Count; i++)
            {
                for (var j = 0; j < data.Columns.Count; j++)
                {
                    if (!columnsToDelete.Contains(data.Columns[j].ColumnName))
                    {
                        var colProp = columns.ToList().Where(d => d.Name == data.Columns[j].ColumnName).Select(d => d).FirstOrDefault();
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (Tattr is TableFieldAttribute excelAttrOfT)
                        {
                            if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                            {
                                var Tcolumnname = excelAttrOfT.ColumnName;
                                if (excelAttrOfT.ColumnDataType == DataType.Date)
                                {
                                    var dataValue = ((DateTimeOffset)data.Rows[i].ItemArray[j]).ToString("dd/MM/yyyy");
                                    table.AddCell(dataValue);
                                }
                                else if (excelAttrOfT.ColumnDataType == DataType.Time)
                                {
                                    var dataValue = data.Rows[i].ItemArray[j] != DBNull.Value ? ((DateTimeOffset)data.Rows[i].ItemArray[j]).ToString(@"HH: mm") : "";
                                    table.AddCell(dataValue);
                                }
                                else
                                {
                                    var dataValue = data.Rows[i].ItemArray[j].ToString();
                                    table.AddCell(dataValue);
                                }

                            }
                        }
                    }
                }

            }

            return table;
        }
        public DataTable RemoveColumnsAndChangeHeader(long companyId, IEnumerable<PropertyInfo> columns, DataTable dt, List<string> columnsToDelete)
        {
            var ordinal = 0;
            foreach (var colProp in columns)
            {
                if (columnsToDelete != null && columnsToDelete.Contains(colProp.Name))
                {
                    dt.Columns.Remove(colProp.Name);
                }
                else
                {
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT)
                    {
                        var Tcolumnname = excelAttrOfT.ColumnName;

                        if (excelAttrOfT.PDFColCategory != PDFColCategory.Table)
                        {
                            dt.Columns.Remove(colProp.Name);
                        }
                        else
                        {
                            if (_useNomenclature && excelAttrOfT.NomenclatureRequirement == true)
                            {
                                var listforheader = excelAttrOfT.ColumnName.Split(' ').ToList();
                                var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                                var updatelist = listforheader.Remove(listforheader[0]);
                                var striingotherthennomenclature = String.Join(" ", listforheader);
                                Tcolumnname = nomenclaturename + " " + striingotherthennomenclature;
                                dt.Columns[colProp.Name].SetOrdinal(ordinal);
                                dt.Columns[colProp.Name].ColumnName = Tcolumnname;
                            }
                            else
                            {
                                dt.Columns[colProp.Name].SetOrdinal(ordinal);
                                dt.Columns[colProp.Name].ColumnName = excelAttrOfT.ColumnName;
                            }

                            ordinal++;
                        }
                    }
                }
            }
            return dt;
        }
        public DataTable RemoveColumns(long companyId, IEnumerable<PropertyInfo> columns, DataTable dt, List<string> columnsToDelete)
        {
            var ordinal = 0;
            foreach (var colProp in columns)
            {
                if (columnsToDelete != null && columnsToDelete.Contains(colProp.Name))
                {
                    dt.Columns.Remove(colProp.Name);
                }
                else
                {
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT)
                    {
                        var Tcolumnname = excelAttrOfT.ColumnName;

                        if (excelAttrOfT.PDFColCategory != PDFColCategory.Table)
                        {
                            dt.Columns.Remove(colProp.Name);
                        }
                        else
                        {
                            dt.Columns[colProp.Name].SetOrdinal(ordinal++);
                        }
                    }
                }
            }
            return dt;
        }

        public PdfPTable CreateTotalTable<T>(long companyId, T data, List<string> columnsToDelete)
        {
            if (data == null)
                return null;
            var columns = data?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
            var list = new List<T>();
            list.Add(data);
            var dt = ConvertToDataTable.GetDataTableFromList(list, "Aggregates");
            dt = RemoveColumns(companyId, columns, dt, columnsToDelete);
            var totalTable = CreateDataTableWithoutHeader(dt, columns, columnsToDelete);
            return totalTable;
        }
        public List<PdfPTable> CreateFlatTotalTable<T>(long companyId, T data, List<string> columnsToDelete)
        {
            if (data == null)
                return null;
            var columns = data?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
            var list = new List<T>();
            list.Add(data);
            var dt = ConvertToDataTable.GetDataTableFromList(list, "Aggregates");
            dt = RemoveColumns(companyId, columns, dt, columnsToDelete);
            var totalTable = CreateFlatDataTable(data, companyId, "", new List<string>(), 2, columnsToDelete);
            return totalTable;
        }
    }
}
