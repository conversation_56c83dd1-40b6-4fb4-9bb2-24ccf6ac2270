﻿using EmbeddedEmails.Core.Repositories;
using Libraries.CommonEnums;
using Library.StringHelpers.Extensions;
using System;
using System.Collections.Generic;
using System.Text;

namespace EmbeddedEmails.Core.Helpers
{
    public class CompanySettings
    {
        private readonly Dictionary<string, object> settings;
        private readonly long companyId;

        private CompanySettings(Dictionary<string, object> settings)
        {
            this.settings = settings;
        }
        public CompanySettings(long companyId, ICompanySettingsRepository companySettingsRepository)
        {
            this.companyId = companyId;
            this.settings = companySettingsRepository.GetSettings(companyId);
        }
        public static CompanySettings Initialize(Dictionary<string, object> settings)
        {
            return new CompanySettings(settings);
        }

        public JourneyPlanType GetJourneyPlanType
        {
            get
            {
                try
                {

                    if (settings.ContainsKey("JourneyPlanType"))
                    {
                        var journeyPlanType = (string)settings["JourneyPlanType"];
                        return journeyPlanType.TestNullAssign(JourneyPlanType.Default);
                    }
                    else
                    {
                        return JourneyPlanType.Default;
                    }
                }
                catch (Exception)
                {
                    return JourneyPlanType.Default;
                }

            }
        }

        
        public int MonthStartDate
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("monthStartDate"))
                    {
                        return (int)(long)settings["monthStartDate"];
                    }
                    else
                        return 1;
                }
                catch (Exception)
                {
                    return 1;
                }
            }
        }

        public bool UsesSalesinAmount
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("UsesSalesinQuantity"))
                    {
                        return (bool)settings["UsesSalesinQuantity"];
                    }
                    else
                        return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public string CurrencySymbol
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("CurrencySymbol"))
                    {
                        return (string)settings["CurrencySymbol"];
                    }
                    else
                    {
                        return "₹";
                    }
                }
                catch (Exception)
                {
                    return "₹";
                }
            }
        }

        public int YearStartMonth
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("yearStartMonth"))
                    {
                        return (int)(long)settings["yearStartMonth"];
                    }
                    else
                        return 4;
                }
                catch (Exception)
                {
                    return 4;
                }
            }
        }

        public bool IsUsingPJP => GetJourneyPlanType == JourneyPlanType.PJPFourWeekly || GetJourneyPlanType == JourneyPlanType.PJPOpen;

        
    }
}
