﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.Core.Models
{
    public class EmployeeDetails
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string EmployeeErpId { get; set; }
        public string EmailId { get; set; }
        public string ContactNo { get; set; }
        public string ParentEmailId { get; set; }
        public string Designation { get; set; }
        public string EmployeeAttributeText1 { get; set; }
        public string EmployeeAttributeText2 { get; set; }
        public double? EmployeeAttributeNumber1 { get; set; }
        public double? EmployeeAttributeNumber2 { get; set; }
    }
}
