﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.Core.Models
{
    public class AttendanceSalesModel
    {
        public long Id { get; set; }

        public long LocationId { get; set; }

        public long EmployeeId { get; set; }

        public DateTimeOffset DeviceTime { get; set; }
        public int? BeatId { get; set; }

        public long? DistributorId { set; get; }
        public string RemarksDistributor { set; get; }
        //public decimal Discount { get; set; }
        public double OrderInRevenue { get; set; }
        public double OrderInUnits { get; set; }
        //public double DiscountInAmount { get; set; }
        public string InvoiceNo { get; set; }
        //public decimal? CGST { get; set; }
        //public decimal? SGST { get; set; }
        //public decimal? IGST { get; set; }


        //Sales
        public double Discount { get; set; }
        public double? NearExpiryStockQuantity { get; set; }
        public decimal SchemeCashDiscount { get; set; }
        public double PTR { get; set; }
        public double? MRP { get; set; }
        public long ProductId { get; set; }

        public double ReturnQuantity { get; set; }
        public double SaleValue { get; set; }
        public double? StockQuantity { get; set; }
        public double? DistributorCashDiscount { get; set; }
        public double StandardUnitConversionFactor { get; set; }
        public double SuperUnitConversionFactor { get; set; }
        public decimal? SGST { get; internal set; }
        public decimal? IGST { get; internal set; }
        public decimal? CGST { get; internal set; }
        public decimal? VAT { get; internal set; }
        public decimal? Excise { get; internal set; }
    }
}
