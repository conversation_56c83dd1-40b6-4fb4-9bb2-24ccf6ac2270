﻿using Microsoft.EntityFrameworkCore;
using ShareOrderonEmail.Core.Models;
using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using ShareOrderonEmail.DbStorage.DbContexts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.DbStorage.Repositories.MasterRepositories
{
    public class LocationRepository:ILocationRepository
    {
        private readonly MasterDbContext db;
        public LocationRepository(MasterDbContext db)
        {
            this.db = db;
        }
        public async Task<List<LocationCore>> GetlocDetails(long companyId, List<long> locationIds)
        {
            var loc = await db.Locations.Where(l => l.CompanyId == companyId && locationIds.Contains(l.Id)).Select(l => new LocationCore
            {
                Id = l.Id,
                OwnersName = l.OwnersName,
                OwnersNo = l.OwnersNo,
                ShopName = l.ShopName,
                GSTIN = l.GSTIN,
                PlaceOfDelivery = l.PlaceOfDelivery,
                Address = l.Address,
                ErpId = l.ErpId,
                AttributeNumber1 = l.AttributeNumber1,
                AttributeNumber2 = l.AttributeNumber2,
                AttributeNumber3 = l.AttributeNumber3,
                AttributeText1 = l.AttributeText1,
                AttributeText2 = l.AttributeText2,
                AttributeText3 = l.AttributeText3,
            }).ToListAsync();
            return loc;
        }
    }
}
