﻿using EmbeddedEmails.Core.DbModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace EmbeddedEmails.DbStorage.DbContexts
{
    public class ReportDbContext : DbContext
    {
        public ReportDbContext(DbContextOptions<ReportDbContext> options) : base(options)
        {

        }
        public DbSet<DayStart> DayStarts { get; set; }

        public DbSet<Attendance> Attendances { get; set; }

        public DbSet<PlannedJourney> PlannedJourney { get; set; }

        public override int SaveChanges()
        {
            throw new NotImplementedException();
        }

        public override int SaveChanges(bool acceptAllChangesOnSuccess)
        {
            throw new NotImplementedException();
        }

        public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new NotImplementedException();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new NotImplementedException();
        }


        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {

        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<DayStart>().HasKey(p => p.SessionId);

            modelBuilder.Entity<Attendance>().HasKey(p => p.AttendanceGuid);

            modelBuilder.Entity<PlannedJourney>()
                .HasKey(p => new { p.ESMId, p.JourneyDateKey, p.ShopId });

            base.OnModelCreating(modelBuilder);
        }
    }

}
