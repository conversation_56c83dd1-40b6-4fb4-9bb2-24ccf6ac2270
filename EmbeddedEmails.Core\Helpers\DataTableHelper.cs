﻿using EmbeddedEmails.Core.Models;
using FileGenerator.Attributes;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;

namespace EmbeddedEmails.Core.Helpers
{
    public static class DataTableHelpers
    {
        public static DataTable ToDataTable<T>(List<T> items, List<string> includedColumns, Dictionary<string, string> nomenclatureDict)
        {
            DataTable dataTable = new DataTable(typeof(T).Name);
            //Get all the properties
            List<PropertyInfo> Props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance).ToList();
            Props = Props.Where(s=> includedColumns.Contains(s.Name)).OrderBy(p => includedColumns.IndexOf(p.Name)).ToList();

            foreach (PropertyInfo prop in Props)
            {
                if (includedColumns.Contains(prop.Name))
                {
                    //Defining type of data column gives proper data table 
                    var type = (prop.PropertyType.IsGenericType && prop.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>) ? Nullable.GetUnderlyingType(prop.PropertyType) : prop.PropertyType);
                    //Setting column names as Property names
                    var name = /*prop.GetCustomAttribute<ColumnHeader>()?.Name ??*/ prop.Name;
                    dataTable.Columns.Add(name, typeof(string));
                }
            }
            foreach (T item in items)
            {
                var values = new List<string>();
                foreach (PropertyInfo prop in Props)
                {
                    if (includedColumns.Contains(prop.Name))
                    {
                        string val = "";
                        //inserting property values to datatable rows
                        if (prop.PropertyType == typeof(DateTimeOffset) || prop.PropertyType == typeof(DateTimeOffset?))
                        {
                            if (prop.Name == "DayStartDate")
                            {
                                DateTimeOffset? cellVal = (DateTimeOffset?)prop.GetValue(item, null);
                                val = (cellVal.HasValue ? cellVal.Value.ToString("dd/MM/yyyy") : "-").ToString();
                            }
                            else if (prop.Name == "TimeSpentRetailinginMinutes" || prop.Name == "TimeSpentRetailing")
                            {
                                DateTimeOffset? cellVal = (DateTimeOffset?)prop.GetValue(item, null);
                                val = (cellVal.HasValue ? cellVal.Value.ToString("HH:mm") : "-").ToString();
                            }
                            else if (prop.Name == "TotalActivityTime")
                            {
                                DateTimeOffset? cellVal = (DateTimeOffset?)prop.GetValue(item, null);
                                val = (cellVal.HasValue ? cellVal.Value.ToString("HH:mm") : "-").ToString();
                            }
                            else if (prop.Name == "TotalTime")
                            {
                                DateTimeOffset? cellVal = (DateTimeOffset?)prop.GetValue(item, null);
                                val = (cellVal.HasValue ? cellVal.Value.ToString("HH:mm") : "-").ToString();
                            }
                            else
                            {
                                DateTimeOffset? cellVal = (DateTimeOffset?)prop.GetValue(item, null);
                                val = (cellVal.HasValue ? cellVal.Value.ToString("hh:mm tt") : "-").ToString();
                            }
                        }
                        else if (prop.PropertyType == typeof(double))
                        {
                            val = (prop.GetValue(item, null) != null) ? Math.Round(Convert.ToDouble((prop.GetValue(item, null))), 2).ToString() : "";
                        }
                        else if (prop.Name == "FirstActivityTime" || prop.Name == "LastActivityTime")
                        {
                            var date = (DateTime?)prop.GetValue(item, null);
                            DateTimeOffset? cellVal = (DateTimeOffset?)date;
                            val = (cellVal.HasValue ? cellVal.Value.ToString("HH:mm") : "-").ToString();
                        }
                        else if (prop.Name == "FirstOtherActivityTime" || prop.Name == "LastOtherActivityTime")
                        {
                            var date = (DateTime?)prop.GetValue(item, null);
                            DateTimeOffset? cellVal = (DateTimeOffset?)date;
                            val = (cellVal.HasValue ? cellVal.Value.ToString("HH:mm") : "-").ToString();
                        }
                        else if (prop.GetCustomAttribute<TableFieldAttribute>().NomenclatureRequirement)
                        {
                            var cellValue = prop.GetValue(item, null)?.ToString();
                            val = cellValue != null ? (nomenclatureDict.ContainsKey(cellValue) ? nomenclatureDict[cellValue] : cellValue) : "";
                        }
                        else
                        {
                            val = (prop.GetValue(item, null) != null) ? (prop.GetValue(item, null)).ToString() : "";
                        }

                        values.Add(val);
                    }
                }
                dataTable.Rows.Add(values.ToArray());
            }
            //put a breakpoint here and check datatable
            return dataTable;
        }
    }
}
