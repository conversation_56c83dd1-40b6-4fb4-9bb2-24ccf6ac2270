﻿using FileGenerator.Interfaces;
using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ShareOrderonEmail.Core.Helpers
{
    public class Nomenclature : INomenclatureSpecifier
    {
        private readonly INomenclatureRepository nomenclatureRepository;
        public Nomenclature(INomenclatureRepository nomenclatureRepository)
        {
            this.nomenclatureRepository = nomenclatureRepository;
        }

        public CompanyNomenclatureSpecifier GetSpecifierForCompany(long companyId, bool getAll = false)
        {
            //Date: 28th Sep 2020
            //Link: https://app.asana.com/0/43051116721058/1195308828573106/f
            //Reason: Retrieving nomeclature from the nomeclature dictionary and not from mapped company
            return new CompanyNomenclatureSpecifier(GetDictionary(companyId, getAll));
        }

        static Dictionary<string, long> CompanyNomenclatures = new Dictionary<string, long>
        {
            ["ASM"] = 1,
            ["RSM"] = 2,
            ["ESM"] = 3,
            ["Distributor"] = 4,
            ["Beat"] = 5,
            ["Outlet"] = 6,
            ["SaleValue "] = 7,
            ["ActualSales"] = 8,
            ["Targets"] = 9,
            ["Attendance"] = 10,
            ["Supplies"] = 11,
            //["Beat"] = 12,
            ["ZSM"] = 13,
            ["NSM"] = 14,
            ["GSM"] = 15,
            ["PrimaryCategory"] = 16,
            ["SecondaryCategory"] = 17,
            ["AlternateCategory"] = 18,
            //["EmployeeAreaName"] = 19,
            ["Zone"] = 20,
            ["Region"] = 21,
            ["Territory"] = 22,
            ["PromotedProducts"] = 23,
            ["MustSell"] = 24,
            ["Stockist"] = 25,
            ["SubStockist"] = 26,
            ["SuperStockist"] = 27,
            ["UTC"] = 28,
            ["UPC"] = 29,
            ["LPC"] = 30,
            ["EffectiveCall"] = 31,
            ["Focused"] = 32,
            ["FocusedOutlet"] = 33,
            ["Unit"] = 34,
            ["StdUnit"] = 35,
            ["AvgValuePerCall"] = 36,
            ["SuggestedQty"] = 37,
            ["ISRSummary"] = 38,
            ["TertiarySales"] = 39,
            ["StockInward"] = 40,
            ["%Billed"] = 41,
            ["%Covered"] = 42,
            ["AvgQuantityUnitPerPC"] = 43,
            ["AttributeText1"] = 46,
            ["AttributeText2"] = 47,
            ["AttributeText3"] = 48,
            ["AttributeText4"] = 49,
            ["AttributeDate1"] = 50,
            ["AttributeDate2"] = 51,
            ["AttributeImage1"] = 52,
            ["AttributeImage2"] = 53,
            ["AttributeImage3"] = 54,
            ["AttributeBoolean1"] = 55,
            ["AttributeBoolean2"] = 56,
            ["AttributeNumber1"] = 61,
            ["AttributeNumber2"] = 62,
            ["AttributeNumber3"] = 63,
            ["AttributeNumber4"] = 64,
            ["ProductDivision"] = 65,
            ["Level7"] = 60,
            ["Level6"] = 59,
            ["Level5"] = 58,
            ["L1Position"] = 71,
            ["L2Position"] = 72,
            ["L3Position"] = 73,
            ["L4Position"] = 74,
            ["L5Position"] = 75,
            ["L6Position"] = 76,
            ["L7Position"] = 77,
            ["L8Position"] = 78,
            ["DistributorAttributeText2"] = 80,
            ["DistributorAttributeText1"] = 81,
            ["DistributorAttributeText3"] = 82,
            ["DistributorAttributeBoolean1"] = 83,
            ["DistributorAttributeBoolean2"] = 84,
            ["DistributorAttributeNumber1"] = 85,
            ["DistributorAttributeNumber2"] = 86,
            ["DistributorAttributeDate1"] = 87,
            ["DistributorAttributeDate2"] = 88,
            ["BeatAttributeText1"] = 89,
            ["BeatAttributeText2"] = 90,
            ["BeatAttributeText3"] = 91,
            ["BeatAttributeText4"] = 92,
            ["BeatAttributeBoolean1"] = 93,
            ["BeatAttributeBoolean2"] = 94,
            ["BeatAttributeNumber1"] = 95,
            ["BeatAttributeNumber2"] = 96,
            ["BeatAttributeNumber3"] = 97,
            ["BeatAttributeNumber4"] = 98,
            ["PTR"] = 123,
            ["SuperUnit"] = 125
        };

        private Dictionary<string, string> GetDictionary(long companyId, bool getAll = false)
        {
            var nomenclatures = nomenclatureRepository.GetMins(companyId);

            //Date: 28th Sep 2020
            //Link: https://app.asana.com/0/43051116721058/1195308828573106/f
            //Reason: Retrieving nomeclature from the nomeclature dictionary and not from mapped company

            if (nomenclatures != null)
            {
                // Date : 10th Oct. 2020
                // ASANA Link: https://app.asana.com/0/43051116721058/1198180101232132/f
                // nomenclatures get null and we adding group by. Added in null check 
                var nomeclaturedict = nomenclatures.GroupBy(n => n.NomenclatureId).ToDictionary(x => x.Key, x => x.FirstOrDefault());
                var oppDictionary = CompanyNomenclatures.ToDictionary(n => n.Value, n => n.Key);

                if (!getAll)
                {
                    return nomenclatures.GroupBy(k => k.NomenclatureId, (nomenClatureRepository, g) => g.FirstOrDefault())
                .Where(k => oppDictionary.ContainsKey(k.NomenclatureId))
                   .ToDictionary(k => oppDictionary[k.NomenclatureId], v => v.DisplayName);
                }
                else
                {
                    Dictionary<string, string> dict = new Dictionary<string, string>();
                    foreach (var item in oppDictionary)
                    {
                        var companyNomeclature = nomeclaturedict.ContainsKey(item.Key) ? nomeclaturedict[item.Key].DisplayName : item.Value;
                        dict.Add(item.Value, companyNomeclature);
                    }
                    return dict;
                }

            }
            else
            {
                return new Dictionary<string, string>();
            }
        }
    }

}
