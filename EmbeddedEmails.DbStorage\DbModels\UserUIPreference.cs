﻿using EmbeddedEmails.Core.Models;
using Libraries.CommonEnums;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace EmbeddedEmails.DbStorage.DbModels
{
   public class UserUIPreference
    {
        public long Id { get; set; }
        public long UserId { get; set; }
        //public bool EmailRequestDeleted { get; set; }
        public PortalUserRole UserRole { get; set; }
        public UIViewName UIViewName { get; set; }
        [StringLength(64)]
        public string PreferenceJsonSource { get; set; }
        [StringLength(1024)]
        public string EmailColumns { get; set; }
        public bool CanEmail { get; set; }
        public long CompanyId { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public virtual Company Company { get; set; }
        [NotMapped]
        public List<string> Columns
        {
            get
            {
                return EmailColumns == null ? new List<string>() : JsonConvert.DeserializeObject<List<string>>(EmailColumns);
            }
            set
            {
                EmailColumns = value == null ? "[]" : JsonConvert.SerializeObject(value);
            }
        }
        public bool IsSummarized { get;  set; }
    }
 
}
