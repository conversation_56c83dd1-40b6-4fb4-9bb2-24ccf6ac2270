﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace EmbeddedEmails.Core.Helpers
{
    public class EmailTemplateTextReplace<T> where T : class
    {

        private readonly string _inputHtml;

        public EmailTemplateTextReplace(string inputHtml)
        {
            _inputHtml = inputHtml;
        }

        public string GetReplacedText(T model)
        {
            var placeHolderValues = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .ToDictionary(p => p.Name.ToUpperInvariant(), p => (p.GetValue(model, null) ?? string.Empty).ToString());

            if (!string.IsNullOrEmpty(_inputHtml))
            {
                return placeHolderValues.Keys.Aggregate(_inputHtml, (current, key) => current.Replace($"<%{key}%>", placeHolderValues[key]));
            }

            return _inputHtml;
        }
    }
}
