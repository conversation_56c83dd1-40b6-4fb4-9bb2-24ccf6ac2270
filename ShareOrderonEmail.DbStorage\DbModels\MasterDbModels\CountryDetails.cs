﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace ShareOrderonEmail.DbStorage.DbModels.MasterDbModels
{
    public class CountryDetails
    {
        [Key]
        public string CountryName { get; set; }
        public int TimeZoneOffsetMinutes { get; set; }

        public string TimeZoneOffsetMinutesStr
        {
            get
            {
                var hour = TimeZoneOffsetMinutes / 60;
                var hourStr = (hour > 0 ? "+" : "") + $"{hour:D2}";
                var minute = TimeZoneOffsetMinutes < 0 ? (-TimeZoneOffsetMinutes % 60) : (TimeZoneOffsetMinutes % 60);
                var minuteStr = $"{minute:D2}";
                return $"GMT{hourStr}:{minuteStr}";
            }
        }
        public string CurrencyName { get; set; }
        public string CurrencySymbol { get; set; }
        public string Language { get; set; }
        public string PhNoPrefix { get; set; }
        public int DigitsInPhNo { get; set; }
        public NumberSystems numberSystem { get; set; }
    }
    public enum NumberSystems
    {
        [Display(Name = "Indian")]
        Indian = 0,
        [Display(Name = "International")]
        International = 1,
    }
}
