﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace ShareOrderonEmail.DbStorage.DbModels.MasterDbModels
{
    public class PositionCodeEntityMapping
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public long PositionCodeId { get; set; }

        public long EntityId { get; set; }

        [Column("IsDetached")]
        public bool IsDeactive { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public PositionCode PositionCode { get; set; }

        public PositionCodeLevel? EntityLevel { get; set; }

        [ForeignKey("EntityId")]
        public Employee Employee { get; set; }
    }

    [Table("PositionCodes")]
    public class PositionCode
    {
        public PositionCode()
        {
            PositionCodeEntityMappings = new HashSet<PositionCodeEntityMapping>();
        }

        public long Id { get; set; }

        public long CompanyId { get; set; }

        public string CodeId { get; set; }

        public string Name { get; set; }

        public PositionCodeLevel Level { get; set; }

        public long? ParentId { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public bool Deleted { get; set; }

        public PositionCode Parent { get; set; }

        public ICollection<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }

        //public ICollection<PositionBeatMapping> PositionBeatMappings { get; set; }

        //public ICollection<PositionDistributorMapping> PositionDistributorMappings { get; set; }

        //public ICollection<RoutePositionMapping> RoutePositionsMappings { get; set; }

        [NotMapped]
        public long EntityId { get; set; }

        public PositionCode ShallowCopy()
        {
            return (PositionCode)MemberwiseClone();
        }
    }
}
