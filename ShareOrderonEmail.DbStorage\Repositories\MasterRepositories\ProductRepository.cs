﻿using Microsoft.EntityFrameworkCore;
using ShareOrderonEmail.Core.Models;
using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using ShareOrderonEmail.DbStorage.DbContexts;
using ShareOrderonEmail.DbStorage.DbModels.MasterDbModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.DbStorage.Repositories.MasterRepositories
{
    public class ProductRepository:IProductRepository
    {
        private readonly MasterDbContext db;
        public ProductRepository(MasterDbContext db)
        {
            this.db = db;
        }
        public async Task<Dictionary<long, ProductCore>> GetProductDetails(long companyId, List<long> ProductIds)
        {
            var taxCategoryDetails = GetCategoryTaxValues(companyId);
            var emp = db.Products.Include(s => s.ProductCategory).ThenInclude(p => p.ProductPrimaryCategory).AsNoTracking();
            var prodDict = await emp.Where(e => e.CompanyId == companyId && ProductIds.Contains(e.Id)).ToDictionaryAsync(a => a.Id, a => new ProductCore
            {
                Name = a.Name,
                ErpCode = a.ErpCode,
                HSNCode = a.HSNCode,
                Unit = a.Unit,
                StandardUnit = a.ProductCategory.ProductPrimaryCategory.StandardUnit,
                SuperUnit = a.ProductCategory.ProductPrimaryCategory.SuperUnit,
                ProductCESSCategoryId = a.ProductCESSCategoryId,
                MRP = a.MRP,
                CGST = taxCategoryDetails.ContainsKey(a.ProductGSTCategoryId ?? 0) ? (decimal?)taxCategoryDetails[a.ProductGSTCategoryId.Value].CGST : null,
                IGST = taxCategoryDetails.ContainsKey(a.ProductGSTCategoryId ?? 0) ? (decimal?)taxCategoryDetails[a.ProductGSTCategoryId.Value].IGST : null,
                SGST = taxCategoryDetails.ContainsKey(a.ProductGSTCategoryId ?? 0) ? (decimal?)taxCategoryDetails[a.ProductGSTCategoryId.Value].SGST : null,
                VAT = taxCategoryDetails.ContainsKey(a.ProductGSTCategoryId ?? 0) ? (decimal?)taxCategoryDetails[a.ProductGSTCategoryId.Value].VAT : null,
            });
            var productCESSCategoryDetails = GetProductCessCategoryDetails(companyId).ToDictionary(x => x.Id, y => y.CESS);
            
            foreach (var item in prodDict)
            {
                item.Value.CESS = productCESSCategoryDetails.TryGetValue(item.Value.ProductCESSCategoryId ?? 0, out var value) ? value : (decimal?)null;
            }
            return prodDict;
        }

        private Dictionary<long, ProductTaxCategoryFlat> GetCategoryTaxValues(long companyId)
        {
            return db.GSTCategoryTaxes.Where(g => g.CompanyId == companyId && !g.IsEnded).ToList().ToDictionary(a => a.CompanyGSTCategoryId, a => new ProductTaxCategoryFlat
            {
                CGST = a.CGST,
                Deleted = a.IsEnded,
                Id = a.Id,
                IGST = a.IGST,
                SGST = a.SGST,
                VAT = a.VAT,
            });
        }

        private List<ProductCESSCategoryFlat> GetProductCessCategoryDetails(long companyId)
        {
            var cESSCats = db.ProductCESSCategories.AsNoTracking().Where(c => c.CompanyId == companyId && !c.Deleted);

            var cESSCategories = cESSCats.Select(a => new { a.Id, a.Name, a.Deleted }).ToList();
            if (cESSCategories != null && cESSCategories.Any())
            {
                var cESSCategoryIds = cESSCategories.Select(i => i.Id).ToList();
                var taxes = db.CESSCategoryTaxes.Where(g => cESSCategoryIds.Contains(g.CompanyCESSCategoryId) && !g.IsEnded)
                    .GroupBy(g => g.CompanyCESSCategoryId, (key, g) => g.OrderByDescending(f => f.Id).FirstOrDefault()).ToList()
                    .ToDictionary(a => a.CompanyCESSCategoryId, v => new { v.CESS });

                return cESSCategories.Select(a => new ProductCESSCategoryFlat
                {
                    CESS = taxes[a.Id].CESS,
                    Id = a.Id,
                    Name = a.Name,
                    Deleted = a.Deleted
                }).ToList();
            }

            return new List<ProductCESSCategoryFlat>();
        }
    }
}
