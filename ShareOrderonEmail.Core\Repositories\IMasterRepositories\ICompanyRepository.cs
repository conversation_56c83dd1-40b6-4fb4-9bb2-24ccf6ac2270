﻿using ShareOrderonEmail.Core.Models;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.Core.Repositories.IMasterRepositories
{
    public interface ICompanyRepository
    {
        Task<List<long>> GetAllActiveCompanyforSpecifiedSetting();
        Task<List<long>> GetAllActiveCompanies();
        Task<bool> ShareOrderToDistributor(long companyId);
        Task<string> ShareOrderToDistributorAt(long companyId);
        Task<string> GetName(long companyId);
        Task<CompanyData> GetCompanyDetails(long companyId);
        Task<bool> UsesClosetoExpiry(long companyId);
        Task<bool> UsesRetailerStock(long companyId);
        Task<List<string>> ShowReturnDetailScreen(long companyId);
        Dictionary<string, object> GetSettings(long companyId);
        String GetCountryDetails(string country);
    }
}
