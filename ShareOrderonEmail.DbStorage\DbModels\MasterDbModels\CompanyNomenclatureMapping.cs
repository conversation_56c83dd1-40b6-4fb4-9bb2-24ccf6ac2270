﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.DbStorage.DbModels.MasterDbModels
{
    public class CompanyNomenclatureMapping
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }
        public int NomenclatureId { get; set; }

        public string Name { get; set; }

        public string HelpText { set; get; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public CompanyNomenclature CompanyNomenclature { get; set; }
    }
    public class CompanyNomenclature
    {
        public int Id { get; set; }
        public string Name { get; set; }

        public string Description { get; set; }

        public bool SendInApp { get; set; }

    }
}
