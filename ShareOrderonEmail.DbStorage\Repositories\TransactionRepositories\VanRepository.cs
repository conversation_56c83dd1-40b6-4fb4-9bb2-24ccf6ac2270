﻿using Microsoft.EntityFrameworkCore;
using ShareOrderonEmail.Core.Models;
using ShareOrderonEmail.Core.Repositories.ITransactionRepositories;
using ShareOrderonEmail.DbStorage.DbContexts;
using ShareOrderonEmail.DbStorage.DbModels.MasterDbModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.DbStorage.Repositories.TransactionRepositories
{
    public class VanRepository : IVanRepository
    {
        private readonly MasterDbContext db;
        public VanRepository(MasterDbContext db)
        {
            this.db = db;
        }

        public async Task<Dictionary<long, VanMasterDto>> GetVanMasterData(long companyId, List<long> employeeId)
        {
            var vanDist = await db.VanDistributorMapping.Where(d => d.CompanyId == companyId && employeeId.Contains((d.EmployeeId ?? 0))).ToListAsync();
            var vanIds = vanDist.Select(d => d.VanId).ToList();
            var vanMasterList = await db.VanMaster.Where(d => d.CompanyId == companyId && vanIds.Contains(d.Id)).ToListAsync();
            var designationdict = await db.Designations.Where(d => d.CompanyId == companyId && !d.IsDeactivated)
                .ToDictionaryAsync(d => d.Id, d => d.Name);

            // Step 1: Convert VanMaster to VanMasterDto and build a lookup
            var vanMasterDtoLookup = vanMasterList
                .Select(v => new VanMasterDto
                {
                    Id = v.Id,
                    VanName = v.VanName,
                    VanRegistrationNumber = v.VanRegistrationNumber,
                    ChassisNumber = v.ChassisNumber,
                    VanCapacity = v.VanCapacity,
                    CreatedAt = v.CreatedAt,
                    LastUpdatedAt = v.LastUpdatedAt,
                    CreationContext = v.CreationContext,
                    Deleted = v.Deleted,
                    VanInvoicePrefix = v.VanInvoicePrefix,
                    CompanyId = v.CompanyId
                })
                .ToDictionary(dto => dto.Id); // Key: VanId

            // Step 2: Create the employee-to-VanMasterDto map
            var employeeToVanMasterMap = vanDist
                .Where(d => d.EmployeeId != null && vanMasterDtoLookup.ContainsKey(d.VanId))
                .GroupBy(d => d.EmployeeId.Value)
                .ToDictionary(
                    g => g.Key,
                    g => vanMasterDtoLookup[g.First().VanId] // assumes one van per employee
                );

            return employeeToVanMasterMap;
        }
    }
}
