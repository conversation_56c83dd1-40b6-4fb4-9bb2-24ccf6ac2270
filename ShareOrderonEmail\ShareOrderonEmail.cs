﻿using System;
using System.Collections.Generic;
using System.Text;
using Library.SlackService;
using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using ShareOrderonEmail.Core.Services;
namespace ShareOrderonEmail
{
    public class ShareOrderonEmail
    {
        private readonly ICompanyRepository companyRepository;
        private readonly ShareOrderonEmailService shareOrderonEmailService;
        private readonly ErrorMessenger errorMessenger;
        public ShareOrderonEmail(ICompanyRepository companyRepository, ShareOrderonEmailService shareOrderonEmailService,ErrorMessenger errorMessenger)
        {
            this.companyRepository = companyRepository;
            this.shareOrderonEmailService = shareOrderonEmailService;
            this.errorMessenger = errorMessenger;
        }
        public async System.Threading.Tasks.Task Process()
        {
            var forDate = DateTime.UtcNow;
            //var time = forDate.TimeOfDay;
            var time = new DateTimeOffset(forDate).ToOffset(TimeSpan.FromMinutes(330)).TimeOfDay;
            try
            {
                await errorMessenger.SendToSlack($"Share Order to Distributor via Email started for All Companies for {forDate.ToShortDateString()}!");
                var CompanieswithActiveSetting = await companyRepository.GetAllActiveCompanyforSpecifiedSetting();
                //var CompanieswithActiveSetting = new List<long> { 10838 };
                foreach (var companyId in CompanieswithActiveSetting)
                {
                    var EmailSendTime = await companyRepository.ShareOrderToDistributorAt(companyId);
                    //var EmailSendTime = "9pm";
                        try
                       {
                            if (EmailSendTime == "9pm" && time >= new TimeSpan(21, 0, 0))
                            //if (EmailSendTime == "9pm")
                            {
                                await shareOrderonEmailService.ShareOrderDataonEmail(companyId);
                            }
                            else if(EmailSendTime == "6pm" && time >= new TimeSpan(18, 0, 0) && time <= new TimeSpan(21, 0, 0))
                            {
                                await shareOrderonEmailService.ShareOrderDataonEmail(companyId);
                            }
                        }
                        catch (Exception ex)
                        {
                            await errorMessenger.SendToSlack($"Share Order via Email error for Company = {companyId} and Date = {forDate.ToShortDateString()}! : {ex}");
                        }
                        
                }
                await errorMessenger.SendToSlack($"Share Order to Distributor via Email done for all Companies for {forDate.ToShortDateString()} ");
            }
            catch (Exception ex)
            {
                await errorMessenger.SendToSlack($"Some Error Share Order to Distributor via Email For Date {forDate.ToShortDateString()}!:{ex.Message}");

            }
        }
    }
}
