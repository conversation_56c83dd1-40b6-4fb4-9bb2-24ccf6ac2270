<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>netstandard2.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="2.0.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="2.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
		<ProjectReference Include="..\ShareOrderonEmail.Core\ShareOrderonEmail.Core.csproj" />
	</ItemGroup>

</Project>