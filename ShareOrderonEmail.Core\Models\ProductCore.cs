﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.Core.Models
{
    public class ProductCore
    {
        public string Name { get; set; }
        public string ErpCode { get; set; }
        public int? HSNCode { get; set; }
        public long? ProductCESSCategoryId { get; set; }
        public decimal? CESS { get; set; }
        public decimal? IGST { get; set; }
        public decimal? CGST { get; set; }
        public decimal? SGST { get; set; }
        public decimal? VAT { get; set; }
        public decimal MRP { get; set; }
        public string Unit { get; set; }
        public string StandardUnit { get; set; }
        public string SuperUnit { get; set; }
    }

    public class ProductCESSCategoryFlat
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public decimal CESS { get; set; }

        public bool Deleted { get; set; }
    }

    public class ProductTaxCategoryFlat
    {
        public long Id { get; set; }

        public decimal IGST { get; set; }
        public decimal CGST { get; set; }
        public decimal SGST { get; set; }
        public decimal VAT { get; set; }

        public bool Deleted { get; set; }
    }
}
