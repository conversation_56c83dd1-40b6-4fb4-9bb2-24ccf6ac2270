﻿using EmbeddedEmails.Core.Helpers;
using EmbeddedEmails.Core.Models;
using EmbeddedEmails.Core.Repositories;
using Libraries.CommonEnums;
using Library.DateTimeHelpers;
using Library.StringHelpers.Extensions;
using System;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

namespace EmbeddedEmails.Core.Services
{
    public class DayReportService
    {
        private readonly ICompanySettingsRepository companySettingsRepository;
        private readonly IDayRepository dayRepository;
        private readonly IAttendanceRepository attendanceRepository;

        public DayReportService(ICompanySettingsRepository companySettingsRepository,
            IDayRepository dayRepository,
            IAttendanceRepository attendanceRepository)
        {
            this.companySettingsRepository = companySettingsRepository;
            this.dayRepository = dayRepository;
            this.attendanceRepository = attendanceRepository;
        }

        public async Task<string> GetDaySummaryText(string recipientName, PortalUserRole userRole,
            long userId, long companyId, DateTime date)
        {
            var imgUp = "http://debug.fieldassist.in/content/email-UpCaret.png";
            var imgDown = "http://debug.fieldassist.in/content/email-DownCaret.png";


            var dateKey = date.GetDateKey();
            var sevenDayPrevDateKey = date.AddDays(-7).GetDateKey();



            var companySettings = CompanySettings.Initialize(companySettingsRepository.GetSettings(companyId));

            var companystartMonthDay = companySettings.MonthStartDate;
            var companystartYearMonth = companySettings.YearStartMonth;
            var isCompanyUsesPjp = companySettings.IsUsingPJP;
            var usesSalesInAmount = companySettings.UsesSalesinAmount;
            var currencySymbol = companySettings.CurrencySymbol;


            var daySummary = await dayRepository.GetDaySummary(companyId, userRole, userId, dateKey);

            var currentDaySales = usesSalesInAmount ? daySummary.Sum(s => s.OrderInRevenue) : daySummary.Sum(s => s.OrderInStdUnits);
            var mtdSales = usesSalesInAmount ? daySummary.Sum(s => s.MTDBookingInRevenue) : daySummary.Sum(s => s.MTDBookingInStdUnits);
            var lmtdSales = usesSalesInAmount ? daySummary.Sum(s => s.LMTDBookingInRevenue) : daySummary.Sum(s => s.LMTDBookingInStdUnits);


            #region sales 
            var saleCrementString = "Lag";
            var lowerSaleValue = mtdSales;
            var pBarCurrentSale = "0%";
            var pBarMaxSale = "100%";
            var isLowerCurrentSale = true;
            var saleColor = "#A8573E";
            var saleImage = imgDown;
            if (lmtdSales < mtdSales)
            {
                lowerSaleValue = lmtdSales;
                saleCrementString = "Increased";
                pBarCurrentSale = "100%";
                pBarMaxSale = "0%";
                isLowerCurrentSale = false;
                saleColor = "#469873";
                saleImage = imgUp;
            }
            var saleCrementVal = Math.Abs(lmtdSales - mtdSales);
            var saleCrementPercent = Math.Round((double)(((saleCrementVal * 100) / (lowerSaleValue == 0 ? 1 : lowerSaleValue))), 2);
            if (isLowerCurrentSale)
            {
                pBarCurrentSale = $"{Math.Round(100 - saleCrementPercent, 2)}%";
            }
            else
            {
                pBarMaxSale = $"{Math.Round(100 - saleCrementPercent, 2)}%";
            }

            #endregion

            var currentDaySummary = new
            {
                TotalUsers = daySummary.Count(),
                TotalActiveUsers = daySummary.Where(d => d.DayStartType != DayStartType.None).Count(),
                TotalInactiveUsers = daySummary.Where(d => d.DayStartType == DayStartType.None).Count(),
                OnLeaveOrOther = daySummary.Where(d => d.DayStartType != DayStartType.Regular).Count(),
                Regular = daySummary.Where(d => d.DayStartType == DayStartType.Regular).Count(),
                OVC = daySummary.Sum(d => d.OVC),
                TC = daySummary.Sum(d => d.TC),
                PC = daySummary.Sum(d => d.PC),
                SC = daySummary.Sum(d => d.SC)
            };

            var weekMaxActiveUsers = await dayRepository.GetMaxActiveEmpForDayIndateRange(companyId, userRole, userId, sevenDayPrevDateKey, dateKey);
            var weekMaxSc = await dayRepository.GetMaxScForDayIndateRange(companyId, userRole, userId, sevenDayPrevDateKey, dateKey);

            #region User Summary
            var auCrementString = "Lag";
            var lowerAuValue = currentDaySummary.TotalActiveUsers;
            var pBarCurrentAu = "0%";
            var pBarMaxAu = "100%";
            var isLowerCurrentAu = true;
            var auColor = "#A8573E";
            var auImage = imgDown;
            if (weekMaxActiveUsers < currentDaySummary.TotalActiveUsers)
            {
                lowerAuValue = weekMaxActiveUsers;
                auCrementString = "Increased";
                pBarCurrentAu = "100%";
                pBarMaxAu = "0%";
                isLowerCurrentAu = false;
                auColor = "#469873";
                auImage = imgUp;
            }
            var auCrementVal = Math.Abs(weekMaxActiveUsers - currentDaySummary.TotalActiveUsers);
            var auCrementPercent = Math.Round((double)(((auCrementVal * 100) / (lowerAuValue == 0 ? 1 : lowerAuValue))), 2);
            if (isLowerCurrentAu)
            {
                pBarCurrentAu = $"{Math.Round(100 - auCrementPercent, 2)}%";
            }
            else
            {
                pBarMaxAu = $"{Math.Round(100 - auCrementPercent, 2)}%";
            }
            #endregion


            if (isCompanyUsesPjp)
            {
                var weekMaxCap = await attendanceRepository.GetWeekMaxCallAgainstPlan(companyId, userRole, userId, sevenDayPrevDateKey, dateKey);
                var cap = await attendanceRepository.GetCallAgainstPlan(companyId, userRole, userId, dateKey);


                var productivity = (double)(cap / (currentDaySummary.SC == 0 ? 1 : currentDaySummary.SC)) * 100;
                var weekProductivity = (double)(weekMaxCap / (weekMaxSc == 0 ? 1 : weekMaxSc)) * 100;

                var prodDiff = Math.Abs(productivity - weekProductivity);
                var prodCrementString = "Lag";
                var prodColor = "#A8573E";
                var prodImg = imgDown;

                if (weekProductivity < productivity)
                {
                    prodCrementString = "Increased";
                    prodColor = "#469873";
                    prodImg = imgUp;
                }


                var model = new DayReportPjp()
                {
                    Date = date.ToString("MMMM dd, yyyy"),
                    Name = recipientName,
                    ActiveUsers = currentDaySummary.TotalActiveUsers,
                    RetailingUsers = currentDaySummary.Regular,
                    SaleColor = saleColor,
                    SaleCrementPercent = $"{ Math.Round(saleCrementPercent, 2)}%",
                    SaleCrementString = saleCrementString,
                    SaleCrementValue = $"{Math.Round(saleCrementVal, 2)}",
                    SaleImage = saleImage,
                    SaleProgBarCurrentWidth = pBarCurrentSale,
                    SaleProgBarMaxWidth = pBarMaxSale,
                    SaleValue = StringHelper.NumberConversion(currentDaySales),
                    LMTD = StringHelper.NumberConversion(lmtdSales),
                    MTD = StringHelper.NumberConversion(mtdSales),
                    MaxActiveUsers = weekMaxActiveUsers,
                    TotalUsers = currentDaySummary.TotalUsers,
                    UserColor = auColor,
                    UserCrementPercent = $"{Math.Round(auCrementPercent, 2)}%",
                    UserCrementString = auCrementString,
                    UserCrementValue = auCrementVal.ToString(),
                    UserImage = auImage,
                    UserProgBarCurrentWidth = pBarCurrentAu,
                    UserProgBarMaxWidth = pBarMaxAu,

                    SC = currentDaySummary.SC,
                    Cap = cap,
                    PCImage = prodImg,
                    PCColor = prodColor,
                    PCCrementString = prodCrementString,
                    ProdCur = $"{Math.Round(productivity, 2)}%",
                    ProdCurBar = $"{Math.Round(productivity > 100 ? 100 : productivity, 2)}%",
                    ProdMaxBar = $"{Math.Round(weekProductivity > 100 ? 100 : weekProductivity),2}%",
                    ProdMax = $"{Math.Round(weekProductivity),2}%",
                    PCCrementPercent = $"{Math.Round(prodDiff, 2)}%",
                };

                var viewpath = string.Format("{0}{1}", GetBasePath(), "/Templates/_dayReportPjp.html");
                var templateText = File.ReadAllText(viewpath);

                var htmlText = new EmailTemplateTextReplace<DayReportPjp>(templateText).GetReplacedText(model);

                return htmlText;
            }
            else
            {
                var weekMaxPC = await dayRepository.GetMaxPcForDayIndateRange(companyId, userRole, userId, sevenDayPrevDateKey, dateKey);
                var weekMaxTC = await dayRepository.GetMaxTcForDayIndateRange(companyId, userRole, userId, sevenDayPrevDateKey, dateKey);

                var productivity = (double)(currentDaySummary.PC / (currentDaySummary.SC == 0 ? 1 : currentDaySummary.SC)) * 100;
                var weekProductivity = (double)(weekMaxPC / (weekMaxTC == 0 ? 1 : weekMaxTC)) * 100;

                var prodDiff = Math.Abs(productivity - weekProductivity);
                var prodCrementString = "Lag";
                var prodColor = "#A8573E";
                var prodImg = imgDown;

                if (weekProductivity < productivity)
                {
                    prodCrementString = "Increased";
                    prodColor = "#469873";
                    prodImg = imgUp;
                }



                var model = new Models.DayReport()
                {
                    Date = date.ToString("MMMM dd, yyyy"),
                    Name = recipientName,
                    ActiveUsers = currentDaySummary.TotalActiveUsers,
                    RetailingUsers = currentDaySummary.Regular,
                    SaleColor = saleColor,
                    SaleCrementPercent = $"{ Math.Round(saleCrementPercent, 2)}%",
                    SaleCrementString = saleCrementString,
                    SaleCrementValue = $"{Math.Round(saleCrementVal, 2)}",
                    SaleImage = saleImage,
                    SaleProgBarCurrentWidth = pBarCurrentSale,
                    SaleProgBarMaxWidth = pBarMaxSale,
                    SaleValue = StringHelper.NumberConversion(currentDaySales),
                    LMTD = StringHelper.NumberConversion(lmtdSales),
                    MTD = StringHelper.NumberConversion(mtdSales),
                    MaxActiveUsers = weekMaxActiveUsers,
                    TotalUsers = currentDaySummary.TotalUsers,
                    UserColor = auColor,
                    UserCrementPercent = $"{Math.Round(auCrementPercent, 2)}%",
                    UserCrementString = auCrementString,
                    UserCrementValue = auCrementVal.ToString(),
                    UserImage = auImage,
                    UserProgBarCurrentWidth = pBarCurrentAu,
                    UserProgBarMaxWidth = pBarMaxAu,

                    SC = currentDaySummary.SC,
                    TC = currentDaySummary.TC,
                    PC = currentDaySummary.PC,
                    PCImage = prodImg,
                    PCColor = prodColor,
                    PCCrementString = prodCrementString,
                    ProdCur = $"{Math.Round(productivity, 2)}%",
                    ProdCurBar = $"{Math.Round(productivity > 100 ? 100 : productivity, 2)}%",
                    ProdMaxBar = $"{Math.Round(weekProductivity > 100 ? 100 : weekProductivity),2}%",
                    ProdMax = $"{Math.Round(weekProductivity),2}%",
                    PCCrementPercent = $"{Math.Round(prodDiff, 2)}%",

                };

                var viewpath = Path.Combine(GetBasePath(), "Templates\\_dayReport.html");
                var templateText = File.ReadAllText(viewpath);

                var htmlText = new EmailTemplateTextReplace<Models.DayReport>(templateText).GetReplacedText(model);

                return htmlText;

            }
        }

        private string GetBasePath()
        {
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AppData");
        }
    }
}
