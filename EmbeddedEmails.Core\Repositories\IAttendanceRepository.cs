﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Libraries.CommonEnums;

namespace EmbeddedEmails.Core.Repositories
{
    public interface IAttendanceRepository
    {
        Task<int> GetWeekMaxCallAgainstPlan(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey);
        Task<int> GetCallAgainstPlan(long companId, PortalUserRole userRole, long userId, long dateKey);
    }
}
