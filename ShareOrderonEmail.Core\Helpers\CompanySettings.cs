﻿
using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.Core.Helpers
{
    public class CompanySettings
    {
        private readonly Dictionary<string, object> settings;
        public readonly string CountryCurrencySymbol;
        public CompanySettings(ICompanyRepository sRepo, long companyId)
        {
            settings = sRepo.GetSettings(companyId);
            var country = settings.ContainsKey("Country") ? settings["Country"] ?? "India" : "India";
            CountryCurrencySymbol = sRepo.GetCountryDetails(country.ToString());
        }

        public string DiscountType
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("DiscountType"))
                    {
                        var distributorMappingType = (string)settings["DiscountType"];
                        return distributorMappingType;
                    }
                    else
                    {
                        return "Default";
                    }
                }
                catch (Exception ex)
                {
                    return "Default";
                }
            }
        }
        public string TypeofTaxCalculation
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("TypeofTaxCalculation"))
                    {
                        var distributorMappingType = (string)settings["TypeofTaxCalculation"];
                        return distributorMappingType;
                    }
                    else
                    {
                        return "Default";
                    }
                }
                catch (Exception ex)
                {
                    return "Default";
                }
            }
        }
        public bool usesIntelligentSchemes
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("usesIntelligentSchemes"))
                    {
                        return (bool)settings["usesIntelligentSchemes"];
                    }
                    else
                    {
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    return false;
                }
            }
        }
        public bool UsesConversionFactor
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("UsesConversionFactor"))
                    {
                        return (bool)settings["UsesConversionFactor"];
                    }
                    else
                    {
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    return false;
                }
            }
        }
        public bool usesCloseToExpiry
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("usesCloseToExpiry"))
                    {
                        return (bool)settings["usesCloseToExpiry"];
                    }
                    else
                    {
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    return false;
                }
            }
        }
        public bool IsUsesRetailerStock
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("IsUsesRetailerStock"))
                    {
                        return (bool)settings["IsUsesRetailerStock"];
                    }
                    else
                    {
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    return true;
                }
            }
        }
        public bool DistributorCashDiscount
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("DistributorCashDiscount"))
                    {
                        return (bool)settings["DistributorCashDiscount"];
                    }
                    else
                    {
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    return false;
                }
            }
        }
    }
}
