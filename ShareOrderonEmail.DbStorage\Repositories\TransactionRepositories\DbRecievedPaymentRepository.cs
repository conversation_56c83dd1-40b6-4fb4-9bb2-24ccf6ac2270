﻿using Microsoft.EntityFrameworkCore;
using ShareOrderonEmail.Core.Models;
using ShareOrderonEmail.Core.Repositories.ITransactionRepositories;
using ShareOrderonEmail.DbStorage.DbContexts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.DbStorage.Repositories.TransactionRepositories
{
    public class DbRecievedPaymentRepository : IDbReceivedPaymentRepository
    {
        private readonly TransactionDbContext db;
        public DbRecievedPaymentRepository(TransactionDbContext db)
        {
            this.db = db;
        }
        public async Task<Dictionary<long,ReceivedPaymentCore>> GetPaymentDetails(List<long> attendanceIds)
        {
            return await db.DbRecievedPayment.Where(a => attendanceIds.Contains(a.AttendanceId)).ToDictionaryAsync(a => a.AttendanceId, a => new ReceivedPaymentCore
            {
                Amount = a.Amount,
                ModeofPayment = a.ModeofPayment
            });
        }
    }
}
