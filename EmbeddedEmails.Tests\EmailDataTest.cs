using EmbeddedEmails.Core.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ReportSender;
using ReportSender.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EmbeddedEmails.Tests
{
    [TestClass]
    public class EmailDataTest
    {
        private ServiceProvider provider;

        [TestInitialize]
        public void Initialise()
        {
            Environment.SetEnvironmentVariable("BuildEnvironment", "ManageTesting");
            Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageReadOnly.vault.azure.net/");
            var config = Configuration.GetConfiguration();

            IServiceCollection serviceCollection = new ServiceCollection();
            Dependencies.SetUp(serviceCollection, config);
            provider = serviceCollection.BuildServiceProvider();


        }

        [TestMethod]
        public void TestMethod1()
        {
            var dayService = provider.GetRequiredService<DayReportService>();
            var htmlString = dayService.GetDaySummaryText("Anuj", Libraries.CommonEnums.PortalUserRole.GlobalAdmin,
                21, 10198, new System.DateTime(2018, 04, 16)).Result;
        }

        [TestMethod]
        public async Task RunEmployeeSummaryService()
        {
            //var emailProcessor = provider.GetRequiredService<DailyEmailReportService>();
            //await emailProcessor.SendEmployeeSummaryEmail(DateTime.UtcNow.AddDays(-1));

            //to log in slack
            var emailProcessor = provider.GetRequiredService<TriggerProcessor>();
            await emailProcessor.Process();
        }

        [TestMethod]
        public async Task EmployeeSummaryService()
        {
            var emailProcessor = provider.GetRequiredService<DayStartReportService>();
            await emailProcessor.GetEmployeeSummary("Anuj", new List<string>() { "ESMName", "TC", "PC" }, 10235, Libraries.CommonEnums.PortalUserRole.GlobalAdmin, 0, new DateTime(2019, 09, 25), false, true);
        }
    }
}
