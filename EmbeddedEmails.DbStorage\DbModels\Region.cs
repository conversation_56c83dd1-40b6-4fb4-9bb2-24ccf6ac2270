﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace EmbeddedEmails.DbStorage.DbModels
{
    [Table("Regions")]
    public class Region
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public long? ZoneId { get; set; }
        public long CompanyId { get; set; }
        public bool IsDeactive { get; set; }

        public Zone Zone { get; set; }
    }
    [Table("FACompanyZones")]
    public class Zone
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public long Company { get; set; }
        public bool IsDeactive { get; set; }
    }
    [Table("Territories")]
    public class Territory
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public long? RegionId { get; set; }

        public Region Region { get; set; }
    }

    [Table("LocationBeats")]
    public class Beat
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public long? TerritoryId { get; set; }

        public string ErpId { get; set; }
        public Territory Territory { get; set; }
    }
}
