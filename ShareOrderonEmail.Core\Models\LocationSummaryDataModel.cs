﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.Core.Models
{
    public class LocationSummaryDataModel
    {
        public string EmployeeName { get; set; }
        public string EmployeeContactNo { get; set; }
        public string EmployeeErpId { get; set; }
        public string Designation { get; set; }
        public string EmployeeAttributeText1 { get; set; }
        public string EmployeeAttributeText2 { get; set; }
        public double? EmployeeAttributeNumber1 { get; set; }
        public double? EmployeeAttributeNumber2 { get; set; }
        public string VanName { get; set; }
        public string VanRegistrationNumber { get; set; }
        public string DistributorName { get; set; }
        public string DistributorContactNo { get; set; }
        public string DistributorAddress { get; set; }
        public string DistributorGSTIN { get; set; }
        public string DistributorErpId { get; set; }
        public string DistributorAttributeText1 { get; set; }
        public string DistributorAttributeText2 { get; set; }
        public string DistributorAttributeText3 { get; set; }
        public double? DistributorAttributeNumber1 { get; set; }
        public double? DistributorAttributeNumber2 { get; set; }
        public string CompanyName { get; set; }
        public string CompanyAddress { get; set; }
        public string CompanyGSTIN { get; set; }
        public string BeatName { get; set; }
        public string Date { get; set; }
        public string InvoiceNumber { get; set; }
        public string OrderTime { get; set; }
        public string LocationName { get; set; }
        public string LocationAddress { get; set; }
        public string LocationNumber { get; set; }
        public string AttributeText1 { get; set; }
        public string AttributeText2 { get; set; }
        public string AttributeText3 { get; set; }
        public double? AttributeNumber1 { get; set; }
        public double? AttributeNumber2 { get; set; }
        public double? AttributeNumber3 { get; set; }
        public string GSTIN { get; set; }
        public string PlaceofDelivery { get; set; }
        public string PlaceofSupply { get; set; }
        public string Remark { get; set; }

        public List<SaleDataDetails> DataForSec { get; set; }

        public double RetailersQuantity { get; set; }
        public string RetailerErpId { get; set; }
        public double RetailersOrderValue { get; set; }
        public decimal? RetailerOrderTax { get; set; }
        public decimal? RetailersCGST { get; set; }
        public decimal? RetailersSGST { get; set; }
        public decimal? RetailersIGST { get; set; }
        public decimal? RetailersSchemeDiscount { get; set; }
        public decimal? FOCBenefit { get; set; }
        public decimal? FOCValue { get; set; }
        public double? RetailersDiscount { get; set; }
        public double? RetailersCashDiscount { get; set; }
        public decimal? RetailersNetOrderValue { get; set; }
        public double? RetailerStockQty { get; set; }
        public double? RetailerReturnQty { get; set; }
        public double? RetailerClosetoExp { get; set; }
        public string ModeofPayment { get; set; }
        public decimal? PaymentValue { get; set; }
    }
    public class SaleDataDetails
    {
        public string ProductName { get; set; }
        public string ErpCode { get; set; }
        public string UnitPrice { get; set; }
        public double Quantity { get; set; }
        public double PTR { get; set; }
        public double? FOCPTR { get; set; }
        public double? MRP { get; set; }
        public double Value { get; set; }
        public decimal FOCQty { get; set; }
        public string GST { get; set; }
        public string HSNCode { get; set; }
        public decimal? CESS { get; set; }
        public decimal FOCValue { get; set; }
        public double Discount { get; set; }
        public double StockQty { get; set; }
        public double ReturnQty { get; set; }
        public double Closetoexp { get; set; }
        public string Unit { get; set; }
        public string StdUnit { get; set; }
        public string SuperUnit { get; set; }
        public string AdditionalUnit { get; set; }
        public long QtyInStdUnit { get; set; }
        public long QtyInUnit { get; set; }
        public long QtyInAdditionalUnit { get; set; }
        public decimal QtyInSuperUnit { get; set; }
        public decimal? GSTAmount { get; set; }
        public decimal? TaxAmount { get; set; }
        public string TaxPercent { get; set; }

        public decimal? igst { get; set; }
        public decimal? cgst { get; set; }
        public decimal? sgst { get; set; }

    }
}
