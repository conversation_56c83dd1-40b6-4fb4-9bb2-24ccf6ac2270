﻿using EmbeddedEmails.Core.Repositories;
using EmbeddedEmails.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EmbeddedEmails.DbStorage.Repositories
{
    public class CompanyRepository : ICompanyRepository
    {
        private readonly MasterDbContext db;

        public CompanyRepository(MasterDbContext db)
        {
            this.db = db;
        }

        public async Task<List<long>> GetAllActiveCompanies()
        {
            return await db.Companies.Where(c => !c.IsDeactive).Select(c => c.Id).ToListAsync();
        }
    }
}
