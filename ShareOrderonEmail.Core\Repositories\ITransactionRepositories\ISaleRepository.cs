﻿using ShareOrderonEmail.Core.Models;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.Core.Repositories.ITransactionRepositories
{
    public interface ISaleRepository
    {
        Task<List<SaleCore>> GetSalesData(long companyId, List<long> attendanceIds);
        Task<Dictionary<long, List<RetailerReturn>>> GetRetailerReturns(List<long> attendanceIds);
    }
}
