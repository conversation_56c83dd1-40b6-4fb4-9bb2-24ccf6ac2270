﻿using Libraries.CommonEnums;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmbeddedEmails.DbStorage.DbModels
{
    [Table("ClientEmployees")]
    public class Employee : Manager
    {
        public long Id { get; set; }
        [Column("Deleted")]
        public override bool IsDeactive { get; set; }
        [Column("Company")]
        public override long CompanyId { get; set; }
        public string ClientSideId { get; set; }
        public long? ParentId { get; set; }
        public EmployeeRank Rank { get; set; }
        public long? OldTableId { get; set; }
        public string LocalName { get; set; }
        public string ContactNo { get; set; }
        public Employee Parent { get; set; }
        public long? RegionId { get; set; }
        public bool IsFieldAppuser { get; set; }
        public DateTime? DateOfJoining { set; get; }
        public Region Region { get; set; }
        public bool IsTrainingUser { get; set; }
        public bool IsOrderBookingDisabled { get; set; }

        public override long UserId => Id;
    }
}
