#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/runtime:3.1 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:3.1 AS build
WORKDIR /src
COPY ["EmbeddedEmails/EmbeddedEmails.csproj", "EmbeddedEmails/"]
COPY ["EmbeddedEmails/AppData", "EmbeddedEmails/"]
COPY ["EmbeddedEmails.Core/EmbeddedEmails.Core.csproj", "EmbeddedEmails.Core/"]
COPY ["FileGenerator/FileGenerator.csproj", "FileGenerator/"]
COPY ["Libraries.CommonEnums/Libraries.CommonEnums.csproj", "Libraries.CommonEnums/"]
COPY ["Library.DateTimeHelpers/Library.DateTimeHelpers.csproj", "Library.DateTimeHelpers/"]
COPY ["Library.EmailService/Library.EmailService.csproj", "Library.EmailService/"]
COPY ["Library.Infrastructure/Library.Infrastructure.csproj", "Library.Infrastructure/"]
COPY ["Library.SlackService/Library.SlackService.csproj", "Library.SlackService/"]
COPY ["Library.StorageWriter/Library.StorageWriter.csproj", "Library.StorageWriter/"]
COPY ["Libraries.Cryptography/Libraries.Cryptography.csproj", "Libraries.Cryptography/"]
COPY ["Library.ResilientHttpClient/Library.ResilientHttpClient.csproj", "Library.ResilientHttpClient/"]
COPY ["Library.StringHelpers/Library.StringHelpers.csproj", "Library.StringHelpers/"]
COPY ["EmbeddedEmails.DbStorage/EmbeddedEmails.DbStorage.csproj", "EmbeddedEmails.DbStorage/"]
RUN dotnet restore "EmbeddedEmails/EmbeddedEmails.csproj"
COPY . .
WORKDIR "/src/EmbeddedEmails"
RUN dotnet build "EmbeddedEmails.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "EmbeddedEmails.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "EmbeddedEmails.dll"]