﻿using ShareOrderonEmail.Core.Models;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.Core.Repositories.IMasterRepositories
{
    public interface IDistributorRepository
    {
        Task<Dictionary<long, DistributorCore>> GetDistributorDetails(long companyId, List<long> DistributorIds);

        Task<List<long>> GetPdfConfigs(long companyId);

        Task<Dictionary<long, string>> GetPdfConfigsForDisplayName(long companyId);
    }
}
