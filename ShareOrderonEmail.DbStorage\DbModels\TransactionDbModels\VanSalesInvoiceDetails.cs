﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace ShareOrderonEmail.DbStorage.DbModels.TransactionDbModels
{
    public class VanSalesInvoiceDetails
    {
        public long Id { get; set; }

        public long VanId { get; set; }

        public long CompanyId { get; set; }

        public long EmployeeId { get; set; }

        public string InvoiceNumber { get; set; }

        public DateTime CreatedAt { get; set; }

        public long OrderQuantity { get; set; }

        public long? DistributerId { get; set; }

        public long OutletId { get; set; }

        public decimal InvoiceAmount { get; set; }

        public long AttendanceId { get; set; }

        [ForeignKey("AttendanceId")]
        public Attendance attendance { get; set; }

        public Guid? SettlementId { get; set; }

        [Column("FieldEventId")]
        public long? FieldEventId { get; set; }

        [Column("Excise")]
        public decimal? Excise { get; set; }
    }
}
