﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace ShareOrderonEmail.Core.Models
{
    public class VanSalesInvoiceDetailsDto
    {
        public long Id { get; set; }

        public long VanId { get; set; }

        public long CompanyId { get; set; }

        public long EmployeeId { get; set; }

        public string InvoiceNumber { get; set; }

        public DateTime CreatedAt { get; set; }

        public long OrderQuantity { get; set; }

        public long? DistributerId { get; set; }

        public long OutletId { get; set; }

        public decimal InvoiceAmount { get; set; }
    }
}
