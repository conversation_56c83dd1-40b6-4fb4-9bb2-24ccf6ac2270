﻿using EmbeddedEmails.Core.Models;
using EmbeddedEmails.Core.Repositories;
using EmbeddedEmails.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EmbeddedEmails.DbStorage.Repositories
{
    public class EmployeeRepository:IEmployeeRepository
    {
        private readonly MasterDbContext db;

        public EmployeeRepository(MasterDbContext db)
        {
            this.db = db;
        }
        public async Task<List<FieldUserRegionZone>> GetFieldUserWithRegionZone(List<long> ids)
        {
            return await db.Employee.Where(e => ids.Contains(e.Id)).Select(e => new FieldUserRegionZone
            {
                FieldUserId = e.Id,
                FieldUserName = e.Name,
                Region = e.Region.Name,
                Zone = e.Region.Zone.Name,
                ZoneId = e.Region.ZoneId
            }).ToListAsync();
        }
        public async Task<Dictionary<long, Employee>> Get(List<long> ids)
        {
            return (await db.Employee.Where(e => ids.Contains(e.Id)).Select(e => new Employee()
            {
                Id = e.Id,
                ErpId = e.ClientSideId,
                LocalName = e.LocalName,
                Name = e.Name,
                Rank = e.Rank,
                ReportingManager = e.Parent.Name,
                DateOfJoining = e.DateOfJoining,
                ContactNo = e.ContactNo,
            }).ToListAsync())
            .ToDictionary(e => e.Id, e => e);
        }
    }
}
