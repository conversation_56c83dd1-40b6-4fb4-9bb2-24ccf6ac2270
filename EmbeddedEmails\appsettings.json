﻿{
  "Logging": {
    "IncludeScopes": false,
    "Debug": {
      "LogLevel": {
        "Default": "Warning"
      }
    },
    "Console": {
      "LogLevel": {
        "Default": "Warning"
      }
    }
  },
  "AppSettings": {
    "Storage": "apiblob",
    "Deployment": "dev",
    "NS_DataApiHost": "https://fa-reportapi-debug.fieldassist.io/",
    "DataAPIToken": "56SbkbmV+#?p+dSNgGPNz8"
  },

  "ConnectionStrings": {
    "MasterStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=fadebug;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "StorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=faappapiv3debug;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "TransactionDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Transactions;user id=f2kAdmin;password=newPasswordiAcc2010;",
    "ReportDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Reports;user id=f2kAdmin;password=newPasswordiAcc2010;",
    "ReadOnlyReportDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Reports;user id=f2kAdmin;password=newPasswordiAcc2010;",
    "MasterDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=Test_F2KLocationsNetwork;user id=azure_readonlyLogin;password=****************;MultipleActiveResultSets=true;",
    "RedisCacheConnectionString": "fieldassist.redis.cache.windows.net:6380,password=MMOK4o+URrY+QDG00KFEJyzH9FwxlcOuQiin0UQSwOw=,ssl=True,abortConnect=False"
  },
  "ApplicationInsights": {
    "InstrumentationKey": "edd7c641-f686-4ff1-bec6-a515e51fbae6"
  },
  "Slack": {
    "ApiToken": "******************************************************",
    "ChannelId": "C094PHZ4ED6"
  }
}