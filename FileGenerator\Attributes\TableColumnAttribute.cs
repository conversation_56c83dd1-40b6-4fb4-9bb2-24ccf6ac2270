﻿using FileGenerator.DataTableHelpers;
using System;
using System.Collections.Generic;

namespace FileGenerator.Attributes
{
    [AttributeUsage(AttributeTargets.Property, Inherited = false)]
    public class TableFieldAttribute : Attribute
    {
        public TableFieldAttribute(string ColumnName)
        {
            this.ColumnName = ColumnName;
        }
        private const string CellFormat_Decimal = "#,##0.00";
        private const string CellFormat_Date = "dd-MM-yyyy";
        private const string CellFormat_Time = "HH:mm";
        private const string CellFormat_Percent = "0.00%";
        private const string CellFormat_JustPercent = "0.00\\%";
        private const string CellFormat_PercentInt = "0%";

        public string GetCellFormat()
        {
            switch (ColumnDataType)
            {
                case DataType.Decimal:
                    return CellFormat_Decimal;
                case DataType.Date:
                    return CellFormat_Date;
                case DataType.Time:
                    return CellFormat_Time;
                case DataType.Percentage:
                    return CellFormat_Percent;
                case DataType.PercentageInt:
                    return CellFormat_PercentInt;
                case DataType.JustPercentage:
                    return CellFormat_JustPercent;
                case DataType.String:
                default:
                    return null;
            }
        }

        public string ColumnName { get; private set; }
        public Requirement ColumnRequirement { get; set; }
        public DataType ColumnDataType { get; set; }
        public bool NomenclatureRequirement { get; set; }
        public string NomenclatureUpdated { get; set; }
        public string CellFormat => GetCellFormat();
        public string ConditionalMatchColumn { get; set; }
        public string ConditionalFormat { get; set; }
        public ColGroupType ColGroupType { get; set; }
        public string HyperLinkText { get; set; }
        public PDFColCategory PDFColCategory { get; set; }
        public CompanySetting[] CompanySettingsToCheck { get; set; }
        public bool IsSettingEnabled {get; set;}
    }
    public class Conditional
    {
        public int TargetLower { get; set; }
    }

    public enum ColGroupType
    {
        None,
        Toggle1,
        Toggle2
    }

    public enum Requirement
    {
        HideIfNull,
        Required,
        HideIfZero,
        SettingBased,
        SpecificSettingBased,
        Selected
    }

    public enum CompanySetting
    {
        NotApplicable,
        UsesPositionCodes,
        HighestPositionLevel,
        HighestGeoHierarchy,
        UsesFAUnify
    }

    public enum DataType
    {
        String = 0,
        Decimal = 1,
        Date = 2,
        Time = 4,
        Percentage = 5,
        PercentageInt =6,
        JustPercentage =7,
        Currency = 8,

    }
    public enum PDFColCategory
    {
        None,
        Info,
        Table,
        Group,
    }
}
