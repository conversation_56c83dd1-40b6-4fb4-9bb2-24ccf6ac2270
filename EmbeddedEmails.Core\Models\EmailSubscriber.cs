﻿using System;
using System.Collections.Generic;
using System.Text;
using Libraries.CommonEnums;
using Library.EmailService;

namespace EmbeddedEmails.Core.Models
{
    public class EmailSubscriber
    {
        public PortalUserRole UserRole { get; set; }
        public long UserId { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public long CompanyId { get; set; }
    }
    public class EmailSubscriberForEmployeeSummary:EmailSubscriber
    {
        public bool IsSummarized { get; set; }
        public bool IsPrimaryCategory { get; set; }
        public List<string> EmailColumnsForManager { get; set; }
    }
}
