﻿using EmbeddedEmails.Core.Helpers;
using EmbeddedEmails.Core.Repositories;
using EmbeddedEmails.Core.Services;
using EmbeddedEmails.DbStorage.DbContexts;
using EmbeddedEmails.DbStorage.Repositories;
using FileLogger;
using Library.EmailService;
using Library.SlackService;
using Library.SlackService.Interface;
using Library.StorageWriter;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SlackNet.Extensions.DependencyInjection;
using SlackNet;
using System.Net.Http;
using Library.CommonHelpers;
using System;

namespace ReportSender.Configuration
{
    public static class Dependencies
    {
        public static string storageConnectionString;
        public static string channelId;
        public static void SetUp(IServiceCollection serviceProvider,IConfiguration configuration)
        {

            storageConnectionString = configuration.GetConnectionString("StorageConnectionString");
            var masterStorageConnectionString = configuration.GetConnectionString("MasterStorageConnectionString");

            var slackApiToken = configuration["Slack:ApiToken"] ?? "xoxb-**********-7724326580263-FISHBycTTu9YUPWuy3nwBVlR";
            channelId = configuration["Slack:ChannelId"] ?? "C094PHZ4ED6";

            serviceProvider.AddDbContext<MasterDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("MasterDbConnectionString")));

            serviceProvider.AddDbContext<ReportDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("ReadOnlyReportDbConnectionString")));

            var isDebug = configuration["AppSettings:Deployment"] == "dev";

            serviceProvider.AddScoped<IManagerRepository, ManagerRepository>();
            serviceProvider.AddScoped<ICompanySettingsRepository, CompanySettingsRepository>();
            serviceProvider.AddScoped<IDayRepository, DayRepository>();
            serviceProvider.AddScoped<ICompanyRepository, CompanyRepository>();
            serviceProvider.AddScoped<IUserUIPreferences, UserUIPreferencesRepository>();
            serviceProvider.AddScoped<IAttendanceRepository, AttendanceRepository>();
            serviceProvider.AddScoped<IEmployeeRepository, EmployeeRepository>();
            serviceProvider.AddScoped<IZoneRepository, ZoneRepository>();
            serviceProvider.AddScoped<IBeatRepository, BeatRepository>();


            serviceProvider.AddScoped<StringModel>();
            serviceProvider.AddScoped<DayReportService>();
            serviceProvider.AddTransient<DailyEmailReportService>();
            serviceProvider.AddScoped<DayStartReportService>();
            serviceProvider.AddScoped<AsyncFileLogger>();



            //helpers
            serviceProvider.AddTransient<ICurrentDateTime, CurrentDateTime>();
            serviceProvider.AddScoped<EmployeeUiConfigBlobReader>(d => new EmployeeUiConfigBlobReader(masterStorageConnectionString));
            serviceProvider.AddScoped<APICallHelper>();


            //Infra
            serviceProvider.AddSingleton<TriggerProcessor>();
            serviceProvider.AddSingleton<IConfiguration>(c => configuration);
            serviceProvider.AddSingleton(s => new AppConfigSettings
            {
                ReportApiBaseUrl = configuration["AppSettings:NSDataApiHost"],
                ReportApiToken = configuration["AppSettings:DataAPIToken"]
            });
            serviceProvider.AddHttpClient().ConfigureHttpClientDefaults(d => d.ConfigureHttpClient(c => c.Timeout = TimeSpan.FromMinutes(10)));
            serviceProvider.AddSlackNet(c => c.UseApiToken(slackApiToken));
            serviceProvider.AddSingleton<ISlackLogHelper>(sp =>
                new SlackLogHelper(
                    sp.GetRequiredService<IHttpClientFactory>(),
                    sp.GetRequiredService<ISlackApiClient>(),
                    masterStorageConnectionString: string.Empty,
                    channelId
                )
            );
            serviceProvider.AddSingleton(e => new EmailSender(masterStorageConnectionString, Library.Infrastructure.QueueService.QueueType.SendEmbeddedEmail));
            serviceProvider.AddSingleton(e => new ErrorMessenger(masterStorageConnectionString, $"EmbeddedEmails", "#embedded-emails", Library.Infrastructure.QueueService.QueueType.SlackCloudLogs));
        }
    }
}
