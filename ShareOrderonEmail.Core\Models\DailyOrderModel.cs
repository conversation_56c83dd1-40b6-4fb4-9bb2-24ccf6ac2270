﻿using FileGenerator.Attributes;
using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.Core.Models
{
    public class DailyOrderModel
    {
        public List<LocationSummaryModel> LocationSummary { get; set; }
        public List<string> ColumnsToDelete { get; set; }
        public LocationSummaryAggregate Agggregates { get; set; }
        public List<string> ColumnsToDeleteFromAggregates { get; set; }
    }
    public class LocationSummaryModel
    {
        [TableField(ColumnName: "Name", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string EmployeeName { get; set; }
        [TableField(ColumnName: "Employee Contact No", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string EmployeeContactNo { get; set; }
        [TableField(ColumnName: "Employee ERP Id", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string EmployeeErpId { get; set; }
        [TableField(ColumnName: "Designation", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string Designation { get; set; }
        [TableField(ColumnName: "Van Registraion Number", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string VanRegistrationNumber { get; set; }
        [TableField(ColumnName: "Van Erp Id", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string VanName { get; set; }
        [TableField(ColumnName: "Employee AttributeText1", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string EmployeeAttributeText1 { get; set; }
        [TableField(ColumnName: "Employee AttributeText2", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string EmployeeAttributeText2 { get; set; }
        [TableField(ColumnName: "Employee AttributeNumber1", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string EmployeeAttributeNumber1 { get; set; }
        [TableField(ColumnName: "Employee AttributeNumber2", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string EmployeeAttributeNumber2 { get; set; }
        [TableField(ColumnName: "Distributor Name", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string DistributorName { get; set; }
        [TableField(ColumnName: "Distributor Contact No", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string DistributorContactNo { get; set; }
        [TableField(ColumnName: "Distributor Address", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string DistributorAddress { get; set; }
        [TableField(ColumnName: "Distributor GSTIN", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string DistributorGSTIN { get; set; }
        [TableField(ColumnName: "Distributor ERP Id", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string DistributorERPId { get; set; }
        [TableField(ColumnName: "Distributor AttributeText1", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string DistributorAttributeText1 { get; set; }
        [TableField(ColumnName: "Distributor AttributeText2", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string DistributorAttributeText2 { get; set; }
        [TableField(ColumnName: "Distributor AttributeText3", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string DistributorAttributeText3 { get; set; }
        [TableField(ColumnName: "Distributor AttributeNumber1", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string DistributorAttributeNumber1 { get; set; }
        [TableField(ColumnName: "Distributor AttributeNumber2", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string DistributorAttributeNumber2 { get; set; }
        [TableField(ColumnName: "Company Name", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string CompanyName { get; set; }
        [TableField(ColumnName: "Company Address", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string CompanyAddress { get; set; }
        [TableField(ColumnName: "Company GSTIN", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string CompanyGSTIN { get; set; }
        [TableField(ColumnName: "Beat", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string BeatName { get; set; }
        [TableField(ColumnName: "Date", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required, ColumnDataType = DataType.Date)]
        public string Date { get; set; }
        [TableField(ColumnName: "Invoice Number", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string InvoiceNumber { get; set; }
        [TableField(ColumnName: "Order Time", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string OrderTime { get; set; }
        [TableField(ColumnName: "Outlet Name", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string LocationName { get; set; }
        [TableField(ColumnName: "Address", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string LocationAddress { get; set; }
        [TableField(ColumnName: "Contact No", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string LocationNumber { get; set; }
        [TableField(ColumnName: "GSTIN", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string GSTIN { get; set; }
        [TableField(ColumnName: "AttributeText1", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string AttributeText1 { get; set; }
        [TableField(ColumnName: "AttributeText2", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string AttributeText2 { get; set; }
        [TableField(ColumnName: "AttributeText3", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string AttributeText3 { get; set; }
        [TableField(ColumnName: "AttributeNumber1", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string AttributeNumber1 { get; set; }
        [TableField(ColumnName: "AttributeNumber2", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string AttributeNumber2 { get; set; }
        [TableField(ColumnName: "AttributeNumber3", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string AttributeNumber3 { get; set; }
        [TableField(ColumnName: "Place of Delivery", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string PlaceofDelivery { get; set; }
        [TableField(ColumnName: "Place of Supply", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string PlaceofSupply { get; set; }
        [TableField(ColumnName: "Remark", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string Remark { get; set; }

        [TableField(ColumnName: "SalesDetails", PDFColCategory = PDFColCategory.Table, ColumnRequirement = Requirement.HideIfNull)]
        public List<SaleDetails> DataForSec { get; set; }

        [TableField(ColumnName: "Retailer ERP Id", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string RetailerErpId { get; set; }
        [TableField(ColumnName: "Outlet Quantity", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string RetailersQuantity { get; set; }
        [TableField(ColumnName: "Outlet Order Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string RetailersOrderValue { get; set; }
        [TableField(ColumnName: "Outlet Order Tax", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string RetailersOrderTax { get; set; }
        [TableField(ColumnName: "Outlet Stock Qty", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string RetailerStockQty { get; set; }
        [TableField(ColumnName: "Outlet Return Qty", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string RetailerReturnQty { get; set; }
        [TableField(ColumnName: "Outlet Close to exp. Qty", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string RetailerClosetoExp { get; set; }
        [TableField(ColumnName: "Outlet Cash Discount", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string RetailersCashDiscount { get; set; }
        [TableField(ColumnName: "Outlet Discount", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string RetailersDiscount { get; set; }
        [TableField(ColumnName: "Outlet CGST Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string RetailersCGST { get; set; }
        [TableField(ColumnName: "Outlet SGST Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string RetailersSGST { get; set; }
        [TableField(ColumnName: "Outlet IGST Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string RetailersIGST { get; set; }
        [TableField(ColumnName: "Outlet CESS Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string RetailersCESS { get; set; }
        [TableField(ColumnName: "Outlet Scheme Discount", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string RetailersSchemeDiscount { get; set; }
        [TableField(ColumnName: "Total Discount", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalDiscount { get; set; }
        [TableField(ColumnName: "Total FOC Benefit", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalFOCBenefit { get; set; }
        [TableField(ColumnName: "Total FOC value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalFocValue { get; set; }
        [TableField(ColumnName: "Total Scheme Discount", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalSchemeDiscount { get; set; }
        [TableField(ColumnName: "Total Tax Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalTax { get; set; }
        [TableField(ColumnName: "Outlet Net Order Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string RetailersNetOrderValue { get; set; }
        
        [TableField(ColumnName: "Outlet Payment Collected", PDFColCategory = PDFColCategory.Info, ColumnRequirement =Requirement.HideIfNull)]
        public string PaymentCollected { get; set; }
        [TableField(ColumnName: "Outlet Mode of Payment", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string ModeofPayment { get; set; }
        public void GetPdfModel(LocationSummaryDataModel resultdata)
        {

        }
    }
    public class SaleDetails
    {
        [TableField(ColumnName: "PRODUCTS", PDFColCategory = PDFColCategory.Table, NomenclatureRequirement = true)]
        public string ProductName { get; set; }
        [TableField(ColumnName: "CODE", PDFColCategory = PDFColCategory.Table)]
        public string ErpCode { get; set; }
        [TableField(ColumnName: "PTR", PDFColCategory = PDFColCategory.Table, ColumnDataType = DataType.Currency)]
        public string PTR { get; set; }
        //public double SaleValue { get; set; }
        [TableField(ColumnName: "QTY", PDFColCategory = PDFColCategory.Table)]
        public string Quantity { get; set; }
        [TableField(ColumnName: "HSN", PDFColCategory = PDFColCategory.Table)] // ColumnRequirement = Requirement.HideIfNull
        public string HSNCode { get; set; }
        [TableField(ColumnName: "UNIT", PDFColCategory = PDFColCategory.Table)] // ColumnRequirement = Requirement.HideIfNull
        public string Unit { get; set; }
        [TableField(ColumnName: "SuperUnit", PDFColCategory = PDFColCategory.Table)] // ColumnRequirement = Requirement.HideIfNull
        public string QuantityInSuperUnit { get; set; }
        [TableField(ColumnName: "MRP", PDFColCategory = PDFColCategory.Table, ColumnDataType = DataType.Currency, NomenclatureRequirement = true)] // ColumnRequirement = Requirement.HideIfNull
        public string MRP { get; set; }
        [TableField(ColumnName: "Unit Price", PDFColCategory = PDFColCategory.Table, ColumnDataType = DataType.Currency)]
        public string UnitPrice { get; set; }
        [TableField(ColumnName: "FREE QTY", PDFColCategory = PDFColCategory.Table)]
        public string FOCQty { get; set; }
        [TableField(ColumnName: "GST", PDFColCategory = PDFColCategory.Table)]
        public string GST { get; set; }
        [TableField(ColumnName: "GST Amount", PDFColCategory = PDFColCategory.Table, ColumnDataType = DataType.Currency)]
        public string GSTAmount { get; set; }
        [TableField(ColumnName: "Tax", PDFColCategory = PDFColCategory.Table)]
        public string TaxPercent { get; set; }
        [TableField(ColumnName: "Tax Amount", PDFColCategory = PDFColCategory.Table, ColumnDataType = DataType.Currency)]
        public string TaxAmount { get; set; }
        [TableField(ColumnName: "CESS", PDFColCategory = PDFColCategory.Table, ColumnDataType = DataType.Currency)] // ColumnRequirement = Requirement.HideIfNull
        public string CESS { get; set; }
        [TableField(ColumnName: "FOC Value", PDFColCategory = PDFColCategory.Table, ColumnDataType = DataType.Currency)]
        public string FOCValue { get; set; }
        [TableField(ColumnName: "Discount", PDFColCategory = PDFColCategory.Table, ColumnDataType = DataType.Currency)]
        public string Discount { get; set; }
        [TableField(ColumnName: "Stock Qty", PDFColCategory = PDFColCategory.Table)]
        public string StockQty { get; set; }
        [TableField(ColumnName: "Return Qty", PDFColCategory = PDFColCategory.Table)]
        public string ReturnQty { get; set; }
        [TableField(ColumnName: "Close to exp", PDFColCategory = PDFColCategory.Table)]
        public string Closetoexp { get; set; }
        [TableField(ColumnName: "TOTAL", PDFColCategory = PDFColCategory.Table, ColumnDataType = DataType.Currency)]
        public string Value { get; set; }

    }
    public class LocationSummaryAggregate
    {
        [TableField(ColumnName: "Total Quantity", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string TotalQuantity { get; set; }
        [TableField(ColumnName: "Total Order Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string TotalOrderValue { get; set; }
        [TableField(ColumnName: "Total Tax", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalTax { get; set; }
        [TableField(ColumnName: "Total Stock Qty", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalStockQty { get; set; }
        [TableField(ColumnName: "Total Return Qty", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalReturnQty { get; set; }
        [TableField(ColumnName: "Total Close to exp. Qty", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalClosetoExp { get; set; }
        [TableField(ColumnName: "Total Cash Discount", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalCashDiscount { get; set; }
        [TableField(ColumnName: "Total Discount", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalDiscount { get; set; }
        [TableField(ColumnName: "Total IGST Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalIGST { get; set; }
        [TableField(ColumnName: "Total CGST Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalCGST { get; set; }
        [TableField(ColumnName: "Total SGST Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalSGST{ get; set; }
        [TableField(ColumnName: "Total CESS Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalCESS { get; set; }
        [TableField(ColumnName: "Total Scheme Discount", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalSchemeDiscount { get; set; }
        [TableField(ColumnName: "Total Net Order Value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.Required)]
        public string TotalNetOrderValue { get; set; }
        [TableField(ColumnName: "Total Payment Collected", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalPaymentCollected { get; set; }
        [TableField(ColumnName: "Total FOC Qty", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]

        public string TotalFocQty { get; set; }
        [TableField(ColumnName: "Total FOC Benefit", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]
        public string TotalFOCBenefit { get; set; }
        [TableField(ColumnName: "Total FOC value", PDFColCategory = PDFColCategory.Info, ColumnRequirement = Requirement.HideIfNull)]

        public string TotalFocValue { get; set; }
    }
}
