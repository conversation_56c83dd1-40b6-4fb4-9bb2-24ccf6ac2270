﻿using Microsoft.EntityFrameworkCore;
using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using ShareOrderonEmail.DbStorage.DbContexts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.DbStorage.Repositories.MasterRepositories
{
    public class BeatRepository : IBeatRepository
    {
        private readonly MasterDbContext db;
        public BeatRepository(MasterDbContext db)
        {
            this.db = db;
        }
        public async Task<Dictionary<long, string>> GetBeatDetails(long companyId, List<long> BeatIds)
        {
            var bea = await db.LocationBeats.Where(e => e.Company == companyId && BeatIds.Contains(e.Id)).ToDictionaryAsync(a => a.Id, a => a.Name);
            return bea;
        }
    }
}
