﻿using EmbeddedEmails.Core.Helpers;
using EmbeddedEmails.Core.Services;
using FileLogger;
using Library.SlackService;
using Library.SlackService.Interface;
using ReportSender.Configuration;
using System;
using System.IO;
using System.Threading.Tasks;

namespace ReportSender
{
    public class TriggerProcessor
    {
        private readonly ICurrentDateTime currentDate;
        private readonly DailyEmailReportService reportProcessor;
        private readonly ErrorMessenger errorMessenger;
        private readonly ISlackLogHelper slackLogHelper;
        private readonly AsyncFileLogger fileLogger;
        private string processorName = "EmbeddedEmailsProcessor";
        private string LogHeader;
        public TriggerProcessor(ICurrentDateTime currentDate, DailyEmailReportService reportProcessor,
            ErrorMessenger errorMessenger, ISlackLogHelper slackLogHelper, AsyncFileLogger fileLogger)
        {
            this.currentDate = currentDate;
            this.reportProcessor = reportProcessor;
            this.errorMessenger = errorMessenger;
            this.slackLogHelper = slackLogHelper;
            this.fileLogger = fileLogger;
        }

        public async Task Process()
        {
            var date = currentDate.IndiaNow.Date.AddDays(-1);
            (string logFileName, FileInfo logFile) = InitializeLogFile();
            try
            {  
                // This is a mandatory step to initialize the fileLogger.
                // Skipping this may result in exceptions.
                await fileLogger.InitializeAsync(logFileName, append: true, $"[{processorName}]: ");
                fileLogger.WriteLine("Started Running EmbeddedEmails!");
                await reportProcessor.SendEmployeeSummaryEmail(currentDate.UtcNow).ConfigureAwait(false);
                fileLogger.WriteLine("EmbeddedEmails Finished Successfully");
            }
            catch (Exception ex)
            {
                fileLogger.WriteLine("Error while Processing EmbeddedEmails");
                fileLogger.WriteLine($"{date} : {ex.GetBaseException().Message}");
                throw;
            }
            finally
            {
                fileLogger.Dispose();
                var logMessage = "EmbeddedEmails Finished Successfully";
                if (await slackLogHelper.SendLogAndFile(logFile, $"{LogHeader}\n" + logMessage, Dependencies.channelId))
                {
                    logFile.Delete();
                }
            }
        }
        protected virtual (string, FileInfo) InitializeLogFile()
        {
            long DateKey = (currentDate.UtcNow.Year * 10000) + (currentDate.UtcNow.Month * 100) + currentDate.UtcNow.Day;
            string LogsDirectory = "Logs";
            var instanceGuid = Guid.NewGuid();
            var logFileName = $"{LogsDirectory}{Path.DirectorySeparatorChar}{processorName}_{DateKey}_logs_{instanceGuid}.txt";
            var logFile = new FileInfo(logFileName);
            LogHeader = $"[{logFile.Name.Replace("_logs", "").Replace(".txt", "")}]: ";
            return (logFileName, logFile);
        }
    }

}

