﻿using EmbeddedEmails.Core.Helpers;
using EmbeddedEmails.Core.Models;
using EmbeddedEmails.Core.Repositories;
using FileGenerator;
using FileGenerator.DataTableHelpers;
using FileGenerator.HelperModels;
using FileLogger;
using Libraries.CommonEnums;
using Library.SlackService;
using OfficeOpenXml;
using ResilientHttpClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace EmbeddedEmails.Core.Services
{
    public class DayStartReportService
    {
        private readonly IDayRepository dayRepository;
        private readonly IBeatRepository beatRepository;
        private readonly ICompanySettingsRepository companySettingsRepository;
        private readonly StringModel stringModel;
        private readonly AppConfigSettings appConfigSettings;
        private readonly ErrorMessenger errorMessenger;
        private readonly AsyncFileLogger fileLogger;

        public DayStartReportService(IDayRepository dayRepository,
            IEmployeeRepository employeeRepository,
            IBeatRepository beatRepository,
            ICompanySettingsRepository companySettingsRepository,
            StringModel stringModel,
            AppConfigSettings appConfigSettings,
            AsyncFileLogger fileLogger)
        {
            this.dayRepository = dayRepository;
            this.beatRepository = beatRepository;
            this.companySettingsRepository = companySettingsRepository;
            this.stringModel = stringModel;
            this.appConfigSettings = appConfigSettings;
            this.fileLogger = fileLogger;
        }
        public async Task<string> GetEmployeeSummary(string managerName, List<string> columns, long companyId, PortalUserRole userRole,
            long userId, DateTime toDate, bool IsSummarized = false, bool IsPrimaryCategory = false)
        {
            var apiBastPath = appConfigSettings.ReportApiBaseUrl;
            var reportingApiActionHelper = new ResilientAPIActions(appConfigSettings.ReportApiToken, (5 * 60));
            DataTable finalDt = null;
            try
            {
                var excelPackage = new ExcelPackage();
                var worksheet = excelPackage.Workbook.Worksheets.Add("Employee Summary");
                PivotColumn pivotColumns = null;
                var apiRouteName = IsSummarized ? "employeeeSummarySummarized" : "employeeeSummary";
                if (IsPrimaryCategory)
                {
                    columns.AddRange(new[] { "Id", "OrderBookingInRevenue", "OrderBookingInUnit", "PrimaryCategory" });
                    apiRouteName = "catergoryWiseEmployeeeSummary";
                    if (IsSummarized)
                    {
                        apiRouteName = "catergoryWiseEmployeeeSummarySummarized";
                    }
                }

                var dataNomeclatureUrl = $"api/nomenclature/getallnomenclature?companyId={companyId}";
                var nomenclatureData = await reportingApiActionHelper.Get<Dictionary<string, string>>($"{apiBastPath}{dataNomeclatureUrl}", 3);
                var dataUrl = $"api/ds/{apiRouteName}?companyId={companyId}&userRole={userRole}&userId={userId}&fromDate={toDate.AddDays(-1).ToString("MM/dd/yyyy")}&toDate={toDate.AddDays(-1).ToString("MM/dd/yyyy")}";
                if (IsSummarized)
                {
                    var data = await reportingApiActionHelper.GetList<EmployeeSummarySumarizedDTO>($"{apiBastPath}{dataUrl}", 3); // setting http client timeout to 5 minutes
                    columns.Insert(0, "ReporteeName");
                    var unnecessaryColumns = typeof(EmployeeSummarySumarizedDTO).GetProperties().Where(s => !columns.Contains(s.Name)).Select(s => s.Name).ToList();
                    data.ForEach(s =>
                    {
                        unnecessaryColumns.ToList().ForEach(p =>
                        {
                            s.GetType().GetProperty(p).SetValue(s, null, null);
                        });
                    });

                    finalDt = DataTableHelpers.ToDataTable(data.OrderBy(s => s.ReporteeName).ToList(), columns, nomenclatureData);
                }
                else
                {
                    var data = await reportingApiActionHelper.GetList<EmployeeSummaryDTO>($"{apiBastPath}{dataUrl}", 3); // setting http client timeout to 5 minutes
                    var unnecessaryColumns = typeof(EmployeeSummaryDTO).GetProperties().Where(s => !columns.Contains(s.Name)).Select(s => s.Name).ToList();
                    data.ForEach(s =>
                    {
                        unnecessaryColumns.ToList().ForEach(p =>
                        {
                            s.GetType().GetProperty(p).SetValue(s, null, null);
                        });
                    });

                    finalDt = DataTableHelpers.ToDataTable(data.OrderBy(s => s.GSM).ThenBy(s => s.NSM).ThenBy(s => s.RSM).ThenBy(s => s.ASM).ThenBy(s => s.Region).ThenBy(s => s.ReportingManager).ThenBy(s => s.ESMName).ToList(), columns, nomenclatureData);
                }
                var htmlstring = "";
                finalDt = SortTable(finalDt, IsPrimaryCategory);
                if (IsPrimaryCategory)
                {
                    pivotColumns = new PivotColumn
                    {
                        ParentColumn = "PrimaryCategory",
                        ValueColumns = new[] { "OrderBookingInRevenue", "OrderBookingInUnit" },
                    };
                    CreatePivotedExcel(companyId, worksheet, finalDt, pivotColumns, IsSummarized);
                    htmlstring = HTMLHelpler.MakeHtmlTable(worksheet, pivotColumns);
                }
                else
                {

                    for (var i = 0; i < finalDt.Columns.Count; i++)
                    {
                        if (finalDt.Columns[i].ColumnName != "ReporteeName")
                        {
                            var customAttribute = typeof(EmployeeSummaryDTO).GetProperty(finalDt.Columns[i].ColumnName).GetCustomAttribute<ColumnHeader>();
                            var columnName = customAttribute != null ? GetNomenclatureFromAttribute(customAttribute.Name, nomenclatureData) : finalDt.Columns[i].ColumnName;
                            //var columnName = nomenclatureData.ContainsKey(finalDt.Columns[i].ColumnName) ? nomenclatureData[finalDt.Columns[i].ColumnName] : customAttribute != null ? customAttribute.Name : finalDt.Columns[i].ColumnName;
                            finalDt.Columns[i].ColumnName = columnName + "$";
                        }

                    }
                    //remove the $ appended
                    for (var i = 0; i < finalDt.Columns.Count; i++)
                    {
                        if (finalDt.Columns[i].ColumnName.Contains("$"))
                        {
                            var columnName = finalDt.Columns[i].ColumnName.Replace("$", "");
                            finalDt.Columns[i].ColumnName = columnName;
                        }
                    }

                    htmlstring = HTMLHelpler.MakeHtmlTable(finalDt);
                }
                stringModel.Value = htmlstring;
                stringModel.Name = managerName;

                var viewpath = Path.Combine(GetBasePath(), "Templates", "EmployeeSummary.html");
                try
                {
                    var templateText = File.ReadAllText(viewpath);

                    var htmlText = new EmailTemplateTextReplace<StringModel>(templateText).GetReplacedText(stringModel);

                    return htmlText;
                }
                catch (Exception ex)
                {
                    fileLogger.WriteLine($"Company: {companyId}, UserId: {userId}, Exception: {ex.Message}");
                    Console.WriteLine($"Failed to get Data for company: {companyId} UserId : {userId} Exception: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                fileLogger.WriteLine($"Company: {companyId}, UserId: {userId}, Exception: {ex.Message}");
                Console.WriteLine($"Failed to get Data for company: {companyId} UserId : {userId} Exception: {ex.Message}");
                throw;
            }

        }
        private string GetNomenclatureFromAttribute(string displayName, Dictionary<string, string> nomenclatureDict)
        {
            var nomenclature = string.Empty;
            nomenclature = displayName.Contains("$") ? GetNomeclatureDefaultValues(displayName, nomenclatureDict) : displayName;
            return nomenclature;
        }
        private string GetNomeclatureDefaultValues(string displayName, Dictionary<string, string> nomenclatureDict)
        {
            var nomenclature = string.Empty;
            var fieldName = displayName.Substring(displayName.IndexOf("$") + 2, displayName.LastIndexOf("$") - displayName.IndexOf("$") - 3);

            switch (fieldName)
            {
                case ("ESM"):
                    var nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
                case ("ASM"):
                    nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
                case ("RSM"):
                    nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
                case ("ZSM"):
                    nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
                case ("GSM"):
                    nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
                case ("NSM"):
                    nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
                case ("Territory"):
                    nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
                case ("Region"):
                    nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
                case ("Zone"):
                    nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
                case ("Beat"):
                    nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
                case ("Outlet"):
                    nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
                case ("Distributor"):
                    nomenclatureFromMemory = nomenclatureDict.ContainsKey(fieldName) ? nomenclatureDict[fieldName] : fieldName;
                    nomenclature = displayName.Replace("$$" + fieldName + "$$", nomenclatureFromMemory);
                    break;
            }
            return nomenclature;

        }
        private string GetBasePath()
        {
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AppData");
        }
        private static DataTable SortTable(DataTable dt, bool IsPrimaryCategory = false)
        {
            DataView dv = dt.DefaultView;
            var pivotColumns = new[] { "Id", "OrderBookingInRevenue", "OrderBookingInUnit", "PrimaryCategory" };
            var columnNames = IsPrimaryCategory ? dt.Columns.Cast<DataColumn>().Where(x => !pivotColumns.Contains(x.ColumnName)).Select(x => x.ColumnName).ToList() : dt.Columns.Cast<DataColumn>().Select(x => x.ColumnName).ToList();
            string sortingList = string.Join(",", columnNames.ToArray());
            sortingList = sortingList + " ASC";
            dv.Sort = sortingList;
            DataTable finalDt = dv.ToTable();
            return finalDt;
        }
        public static void CreatePivotedExcel(long companyId, ExcelWorksheet worksheet, DataTable data, PivotColumn pivotColumns,  bool IsSummarized = false)
        {
            ExcelGenerator excelGenerator = new ExcelGenerator();
            if (IsSummarized)
            {
                excelGenerator.UpdatePivotExcelSheet<EmployeeSummarySumarizedDTO>(companyId, worksheet, data, pivotColumns);
            }
            else
            {
                excelGenerator.UpdatePivotExcelSheet<EmployeeSummaryDTO>(companyId, worksheet, data, pivotColumns);
            }
        }


    }
}
