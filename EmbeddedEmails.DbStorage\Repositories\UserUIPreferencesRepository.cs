﻿using EmbeddedEmails.Core.Models;
using EmbeddedEmails.Core.Repositories;
using EmbeddedEmails.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EmbeddedEmails.DbStorage.Repositories
{
    public class UserUIPreferencesRepository : IUserUIPreferences
    {
        private readonly MasterDbContext db;
        public UserUIPreferencesRepository(MasterDbContext db)
        {
            this.db = db;
        }
        public async Task<List<UserUIPreferenceModel>> GetUserUIPreferences()
        {
            return await db.UserUIPreferences/*.Where(s=>s.CompanyId == 10519)*/.Where(p=>p.PreferenceJsonSource != null && !p.Company.IsDeactive).Select(p => new UserUIPreferenceModel
            {
                CompanyId = p.CompanyId,
                PreferenceJsonSource= p.PreferenceJsonSource,
                Id = p.Id,
                UserId = p.UserId,
                UserRole = p.UserRole,
                LastUpdatedAt = p.LastUpdatedAt,
                CanEmail  = p.CanEmail,
                IsSummarized = p.IsSummarized
            }).ToListAsync();
        }
    }
}
