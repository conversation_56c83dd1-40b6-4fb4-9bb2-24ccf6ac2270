﻿using EmbeddedEmails.Core.Repositories;
using FileLogger;
using Library.EmailService;
using Library.SlackService;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EmbeddedEmails.Core.Services
{
    public class DailyEmailReportService
    {
        private readonly DayStartReportService dayStartReportService;
        private readonly IUserUIPreferences userUIPreferences;
        private readonly DayReportService dayReportService;
        private readonly ICompanyRepository companyRepository;
        private readonly IManagerRepository managerRepository;
        private readonly EmailSender emailSender;
        private readonly AsyncFileLogger fileLogger;

        private readonly List<long> _companiesThatNeedToExecuteFirst = new List<long>() { 11307, 162194 };

        public DailyEmailReportService(DayStartReportService dayStartReportService,
             IUserUIPreferences userUIPreferences,
            DayReportService dayReportService, ICompanyRepository companyRepository,
            IManagerRepository managerRepository, EmailSender emailSender, AsyncFileLogger fileLogger)
        {
            this.dayStartReportService = dayStartReportService;
            this.userUIPreferences = userUIPreferences;
            this.dayReportService = dayReportService;
            this.companyRepository = companyRepository;
            this.managerRepository = managerRepository;
            this.emailSender = emailSender;
            this.fileLogger = fileLogger;
        }


        public async Task SendEmployeeSummaryEmail(DateTime date)
        {
            var userPreferencesList = await userUIPreferences.GetUserUIPreferences();
            var userPreferencesCompanyWise = userPreferencesList.GroupBy(u => u.CompanyId).ToList();

            userPreferencesCompanyWise = userPreferencesCompanyWise.OrderByDescending(i => _companiesThatNeedToExecuteFirst.IndexOf(i.Key)).ToList();
            foreach ( var userPreferences in userPreferencesCompanyWise)
            {
                try
                {
                    var managers = (await managerRepository.GetAllManagersForSubscriptions(userPreferences)).ToList();
                    int i = 0,j=0, count = managers.Count();
                    if (count > 0)
                    {
                        fileLogger.WriteLine($"Started sending daily emailer for company {userPreferences.Key} for {count} managers for date {date.Date.ToString("dd-mm-yyyy")}");
                        Console.WriteLine($"Found {count} Managers to Send Email");
                        foreach (var v in managers)
                        {
                            try
                            {
                                var text = await dayStartReportService.GetEmployeeSummary(v.Name,
                                                                                          v.EmailColumnsForManager,
                                                                                          v.CompanyId,
                                                                                          v.UserRole,
                                                                                          v.UserId,
                                                                                          date,
                                                                                          v.UserRole == Libraries.CommonEnums.PortalUserRole.AreaSalesManager ? false : v.IsSummarized,
                                                                                          v.IsPrimaryCategory
                                                                                          );
                                var emailMessage = new EmailMessage
                                {
                                    Message = text,
                                    IsHTML = true,
                                    Subject = $"Summary for {date.AddDays(-1).ToString("dd/MM/yyyy")}",
                                    To = v.Email,
                                    // Bcc = "<EMAIL>"
                                };
                                await emailSender.Send(emailMessage);
                                Console.WriteLine($"{++i}/{count} : Sending email to {v.Name}[{v.Email}] of company: {v.CompanyId} at {v.UserRole} Level. Generated Email Message :\n" + JsonConvert.SerializeObject(emailMessage));
                                fileLogger.WriteLine($"{++j}/{count} send for {v.Name}[{v.Email}]");
                            }
                            catch (Exception ex)
                            {
                                fileLogger.WriteLine($"Error During Sending email {++j}/{count} to {v.Name}[{v.Email}] of company: {v.CompanyId} at {v.UserRole} Level");
                                fileLogger.WriteLine($"{date} : {ex.Message}");
                            }
                        }
                        fileLogger.WriteLine($"Finished sending daily emailer for company {userPreferences.Key} for {count} managers for date {date.Date.ToString("dd-mm-yyyy")}");

                    }
                }
                catch (Exception ex)
                {
                    //Failed for a company. No Issues we can continue for other Companies
                    fileLogger.WriteLine($"Error while processing daily emails for company {userPreferences.Key} for date {date} at {DateTime.UtcNow} UTC. Exception: {ex}");
                }
            }

        }
    }
}

