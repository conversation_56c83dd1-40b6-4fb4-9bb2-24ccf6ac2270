﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Text;

namespace EmbeddedEmails.Core.Models
{
    public class DayStart
    {
        public long? ASMId { get; set; }
        public long? RSMId { get; set; }
        public long? ZSMId { get; set; }
        public long? NSMId { get; set; }
        public long? GSMId { get; set; }
        public long ESMId { get; set; }
        public string ESMRank { get; set; }
        public DateTime DayStartTime { get; set; }

        public string AssignedReasonCategory { get; set; }
        public long? AssignedBeatId { get; set; }
        public DayStartType DayStartType { get; set; }
        public string ReasonCategory { get; set; }
        public string Reason { get; set; }
        public DateTime? DayEndTime { get; set; }
        public DateTime? FirstCallTime { get; set; }
        public DateTime? LastCallTime { get; set; }
        public DateTime? FirstPCTime { get; set; }
        public DateTime? LastPCTime { get; set; }
        public double TimeSpentRetailingMinutes { get; set; }
        public double TotalTimeMinutes { get; set; }
        public bool IsLate { get; set; }
        public int SC { get; set; }
        public int TC { get; set; }
        public int PC { get; set; }
        public int OVT { get; set; }
        public int OVC { get; set; }
        public int SchemeEffectiveCalls { get; set; }
        public int TelephonicOrders { get; set; }
        public int NewOutletsCreated { get; set; }
        public double NewOutletSalesInUnits { get; set; }
        public double NewOutletSalesInStdUnits { get; set; }
        public double NewOutletSalesInRevenue { get; set; }
        public double OrderInUnits { get; set; }
        public double OrderInStdUnits { get; set; }
        public double OrderInRevenue { get; set; }
        public decimal TotalSchemeDiscount { get; set; }
        public double TotalSchemeQty { get; set; }
        public double ProductWiseDiscount { get; set; }
        public int LinesCut { get; set; }
        public int NoOfStyles { get; set; }
        public int NoOfSecCategories { get; set; }
        public bool IsNormallyEnded { get; set; }
        public long? SelectedBeatId { get; set; }
        public bool IsDayNotStarted { get; set; }
        public double MTDBookingInRevenue { get; set; }
        public double MTDBookingInStdUnits { get; set; }
        public double MTDBookingInUnits { get; set; }
        public string DayStartAddress { get; set; }

        public long? JointWorkingEmployeeId { get; set; }
        public long? AssignedJointWorkingEmployeeId { get; set; }
        public int JointWorkingCalls { get; set; }
    }
}
