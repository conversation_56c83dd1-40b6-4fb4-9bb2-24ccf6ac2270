﻿using EmbeddedEmails.Core.Models;
using EmbeddedEmails.Core.Repositories;
using EmbeddedEmails.DbStorage.DbContexts;
using Libraries.CommonEnums;
using Library.StorageWriter;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EmbeddedEmails.DbStorage.Repositories
{
    public class ManagerRepository : IManagerRepository
    {
        private readonly MasterDbContext db;
        private readonly EmployeeUiConfigBlobReader blobHandler;

        public ManagerRepository(MasterDbContext db, EmployeeUiConfigBlobReader blobHandler)
        {
            this.db = db;
            this.blobHandler = blobHandler;
        }

        public async Task<IEnumerable<EmailSubscriberForEmployeeSummary>> GetAllManagersForSubscriptions(IEnumerable<UserUIPreferenceModel> userUIPreferenceModels)
        {
            var roleWiseSubscriber = userUIPreferenceModels.OrderByDescending(d => d.LastUpdatedAt).GroupBy(s => s.CompanyId).Select(s => new { s.Key, item = s.FirstOrDefault() }).Where(s => s.item.CanEmail).ToList();
            var managers = new List<EmbeddedEmails.DbStorage.DbModels.Manager>();
            foreach (var subscriber in roleWiseSubscriber)
            {
                managers.AddRange(
                                await db.Employee
                                    .Where(c => !c.IsDeactive && !string.IsNullOrEmpty(c.EmailId) && c.LoginGuid != null && c.CompanyId == subscriber.Key && c.UserRole >= PortalUserRole.CompanyAdmin && c.UserRole <= PortalUserRole.AreaSalesManager)
                                    .Select(s=> new DbModels.CompanyAdmin {CompanyId = s.CompanyId,EmailId = s.EmailId , IsDeactive = s.IsDeactive , Name = s.Name , UserRole = s.UserRole , LoginGuid = s.LoginGuid ,Id = s.Id , IsSummarized = subscriber.item.IsSummarized, IsPrimaryCategoryData = subscriber.item.IsPrimaryCategoryData })
                                        .ToListAsync());
                managers.AddRange(await db.CompanyAdmins
                                    .Where(c => !c.IsDeactive && !string.IsNullOrEmpty(c.EmailId) && c.LoginGuid != null && c.CompanyId == subscriber.Key && (c.UserRole == PortalUserRole.CompanyExecutive || c.UserRole == PortalUserRole.CompanyAdmin))
                                    .Select(s => new DbModels.CompanyAdmin { CompanyId = s.CompanyId, EmailId = s.EmailId, IsDeactive = s.IsDeactive, Name = s.Name, UserRole = s.UserRole, LoginGuid = s.LoginGuid, Id = s.Id, IsSummarized = false, IsPrimaryCategoryData = subscriber.item.IsPrimaryCategoryData })
                                        .ToListAsync());
            }
            var preferanceDict = roleWiseSubscriber.ToDictionary(p => p.Key, p => p.item);

            var finalList = new List<EmailSubscriberForEmployeeSummary>();
            foreach (var m in managers)
            {
                var summary = new EmailSubscriberForEmployeeSummary()
                {
                    CompanyId = m.CompanyId,
                    Email = m.EmailId,
                    Name = m.Name,
                    UserId = m.UserId,
                    UserRole = m.UserRole,
                    IsSummarized = m.IsSummarized,
                    IsPrimaryCategory = m.IsPrimaryCategoryData
                };
                summary.EmailColumnsForManager = await GetComlumnsFromJsonSource(preferanceDict[m.CompanyId].PreferenceJsonSource);
                finalList.Add(summary);
            }
            return finalList;
        }
        private async Task<List<string>> GetComlumnsFromJsonSource(string source)
        {
            var jsonContent = JsonConvert.DeserializeObject<List<ColumnFieldPropertyMin>>(await blobHandler.GetBlobAsString(source));
            var result = jsonContent.Where(c => c.Display).OrderBy(c => c.Index).Select(c => c.Name).ToList();
            return result;
        }
    }
}
