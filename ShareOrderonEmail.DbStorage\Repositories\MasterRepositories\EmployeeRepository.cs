﻿using ShareOrderonEmail.DbStorage.DbContexts;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using ShareOrderonEmail.Core.Models;
using System.Threading.Tasks;
using ShareOrderonEmail.Core.Repositories.IMasterRepositories;
using Microsoft.EntityFrameworkCore;
using System.Data.Common;
using ShareOrderonEmail.DbStorage.DbModels.TransactionDbModels;

namespace ShareOrderonEmail.DbStorage.Repositories.MasterRepositories
{
    public class EmployeeRepository: IEmployeeRepository
    {
        private readonly MasterDbContext db;
        private readonly TransactionDbContext dbTransaction;
        public EmployeeRepository(MasterDbContext db, TransactionDbContext dbTransaction)
        {
            this.db = db;
            this.dbTransaction = dbTransaction;
        }
        public async Task<Dictionary<long,EmployeeDetails>> GetEmployeeDetails(long companyId, List<long> EmployeeIds)
        {
            var designationdict = await db.Designations.Where(d => d.CompanyId == companyId && !d.IsDeactivated)
                .ToDictionaryAsync(d => d.Id, d => d.Name);
            var emp = await db.Employees.Where(e => e.Company == companyId && EmployeeIds.Contains(e.Id)).Include(e=> e.Parent).ToDictionaryAsync(a => a.Id, a => new EmployeeDetails
            {
                Name = a.Name,
                EmailId = a.EmailId,
                ContactNo = a.ContactNo,
                ParentEmailId = a.ParentId.HasValue ? a.Parent.EmailId : null,
                Designation = designationdict.ContainsKey(a.DesignationId ?? 0) ? designationdict[a.DesignationId ?? 0] : null,
                EmployeeErpId = a.ClientSideId,
                EmployeeAttributeNumber1 = a.EmployeeAttributeNumber1,
                EmployeeAttributeNumber2 = a.EmployeeAttributeNumber2,
                EmployeeAttributeText1 = a.EmployeeAttributeText1,
                EmployeeAttributeText2 = a.EmployeeAttributeText2,
            });
            return emp;
        }

        public async Task<Dictionary<long, VanSalesInvoiceDetailsDto>> GetVanSalesInvoiceData(List<long> attendanceId)
        {
            var result = await dbTransaction.VanSalesInvoiceDetails
            .Where(s => attendanceId.Contains(s.AttendanceId))
            .GroupBy(s => s.AttendanceId)
            .Select(g => g.OrderByDescending(s => s.CreatedAt).FirstOrDefault())
            .Select(data => new
            {
                data.AttendanceId,
                Dto = new VanSalesInvoiceDetailsDto
                {
                    CompanyId = data.CompanyId,
                    CreatedAt = data.CreatedAt,
                    DistributerId = data.DistributerId,
                    EmployeeId = data.EmployeeId,
                    Id = data.Id,
                    InvoiceAmount = data.InvoiceAmount,
                    InvoiceNumber = data.InvoiceNumber,
                    OrderQuantity = data.OrderQuantity,
                    OutletId = data.OutletId,
                    VanId = data.VanId
                }
            })
            .ToDictionaryAsync(x => x.AttendanceId, x => x.Dto);

            return result;
        }
    }
}
