﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>netcoreapp3.1</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Azure.KeyVault" Version="3.0.5" />
		<PackageReference Include="Microsoft.Azure.WebJobs" Version="3.0.23" />
		<PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="3.1.6" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Library.SlackService\Library.SlackService.csproj" />
		<ProjectReference Include="..\ShareOrderonEmail.Core\ShareOrderonEmail.Core.csproj" />
		<ProjectReference Include="..\ShareOrderonEmail.DbStorage\ShareOrderonEmail.DbStorage.csproj" />
	</ItemGroup>
	<ItemGroup>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="settings.job">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
