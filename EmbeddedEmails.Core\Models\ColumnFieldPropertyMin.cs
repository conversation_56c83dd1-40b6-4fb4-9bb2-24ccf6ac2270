﻿using System;
using System.Collections.Generic;
using System.Text;

namespace EmbeddedEmails.Core.Models
{
    public class ColumnFieldProperty : ColumnFieldPropertyMin
    {
        public string Category { get; set; }
    }


    public class ColumnFieldPropertyMin
    {
        public string Name { get; set; }
        public bool Display { get; set; }
        public int Index { get; set; }
        public string NomenclatureName { get; set; }
    }

    public class ColumnOrderPropertyMin
    {
        public string Name { get; set; }
        public int Index { get; set; }
    }
}
