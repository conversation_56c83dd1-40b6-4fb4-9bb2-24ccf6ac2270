﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.DbStorage.DbModels.MasterDbModels
{
    public class Distributor
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public string EmailId { get; set; }
        public string Name { get; set; }
        public string ContactNo { get; set; }
        public string Address { get; set; }
        public string PlaceOfSupply { get; set; }
        public string GSTIN { get; set; }
        public string ClientSideId { get; set; }
        public string Distributor_AttributeText1 { get; set; }
        public string Distributor_AttributeText2 { get; set; }
        public string Distributor_AttributeText3 { get; set; }
        public double? Distributor_AttributeNumber1 { get; set; }
        public double? Distributor_AttributeNumber2 { get; set; }
    }
}
