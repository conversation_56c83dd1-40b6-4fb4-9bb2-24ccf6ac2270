﻿using EmbeddedEmails.Core.DbModels;
using EmbeddedEmails.Core.Repositories;
using EmbeddedEmails.DbStorage.DbContexts;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EmbeddedEmails.DbStorage.Repositories
{
    public class AttendanceRepository : IAttendanceRepository
    {
        private readonly ReportDbContext db;

        public AttendanceRepository(ReportDbContext db)
        {
            this.db = db;
        }

        private IQueryable<Attendance> GetAttendanceForUser(long companyId, PortalUserRole userRole, long userId)
        {
            return db.Attendances.Where(a => a.CompanyId == companyId).Where(a => (userRole == PortalUserRole.AreaSalesManager && a.ASMId == userId)
                               || (userRole == PortalUserRole.RegionalSalesManager && a.RSMId == userId)
                               || (userRole == PortalUserRole.ZonalSalesManager && a.ZSMId == userId)
                               || (userRole == PortalUserRole.NationalSalesManager && a.NSMId == userId)
                               || (userRole == PortalUserRole.GlobalSalesManager && a.GSMId == userId)
                               || (userRole == PortalUserRole.CompanyAdmin || userRole == PortalUserRole.GlobalAdmin
                               || userRole == PortalUserRole.ChannelPartner || userRole == PortalUserRole.AccountManager || userRole == PortalUserRole.CompanyExecutive));
        }

        public async Task<int> GetCallAgainstPlan(long companyId, PortalUserRole userRole, long userId, long dateKey)
        {
            return await GetAttendanceForUser(companyId, userRole, userId)
                .Where(a => a.CallStartDateKey == dateKey)
                .Join(db.PlannedJourney, a => a.ESMId, p => p.ESMId, (a, p) => p.ShopId)
                .Distinct().CountAsync();
        }

        public async Task<int> GetWeekMaxCallAgainstPlan(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey)
        {
            return await GetAttendanceForUser(companyId, userRole, userId)
                .Where(a => a.CallStartDateKey <= toDateKey && a.CallStartDateKey >= fromDateKey)
                .Join(db.PlannedJourney, a => a.ESMId, p => p.ESMId, (a, p) => new { p.ShopId, a.CallStartDateKey })
                .GroupBy(g => g.CallStartDateKey)
                .MaxAsync(a => a.Select(g => g.ShopId).Distinct().Count());
        }
    }
}
