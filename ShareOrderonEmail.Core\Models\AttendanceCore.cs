﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ShareOrderonEmail.Core.Models
{
    public class AttendanceCore
    {
        public long Id { get; set; }

        public long LocationId { get; set; }

        public long EmployeeId { get; set; }

        public DateTimeOffset DeviceTime { get; set; }

        public DateTimeOffset ServerTime { get; set; }

        public bool IsValid { get; set; }

        public int? BeatId { get; set; }

        public long? DistributorId { set; get; }

        public int CompanyId { get; set; }

        public string NoSalesReason { get; set; }

        public string NoVisitReason { get; set; }

        public int? BeatPlanItemId { get; set; }

        public bool OutOfTurn { get; set; }

        public bool Productive { get; set; }

        public string Remark { set; get; }

        public string OrderRemark { set; get; }

        public string RemarksManagement { set; get; }

        public string RemarksDistributor { set; get; }

        public string RemarksOther { set; get; }

        public string Signature { set; get; }

        public bool IsTelephonic { set; get; }

        public bool IsReviewed { set; get; }

        public Guid Guid { set; get; }

        public long FAEventId { set; get; }

        public bool IsEffectiveCall { get; set; }

        public decimal Discount { get; set; }

        public bool IsNewOutlet { get; set; }
        public double OrderInRevenue { get; set; }
        public double OrderInStdUnits { get; set; }
        public double OrderInUnits { get; set; }
        public double DiscountInAmount { get; set; }
        public bool IsOVC { get; set; }
        public bool IsScheduled { get; set; }
        public double? GeoTaggingDistance { get; set; }
        public DateTime? ExpectedDeliveryDate { get; set; }
        public long? RouteId { get; set; }
        public string InvoiceNo { get; set; }
        public decimal? CGST { get; set; }
        public decimal? SGST { get; set; }
        public decimal? IGST { get; set; }
        public decimal? VAT { get; set; }
        public decimal? Excise { get; set; }
    }
}
