﻿using Microsoft.EntityFrameworkCore;
using ShareOrderonEmail.Core.Models;
using ShareOrderonEmail.Core.Repositories.ITransactionRepositories;
using ShareOrderonEmail.DbStorage.DbContexts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ShareOrderonEmail.DbStorage.Repositories.TransactionRepositories
{
    public class SaleRepository : ISaleRepository
    {
        private readonly TransactionDbContext db;
        public SaleRepository(TransactionDbContext db)
        {
            this.db = db;
        }
        public async Task<List<SaleCore>> GetSalesData(long companyId, List<long> attendanceIds)
        {
            var d = await db.Sales.Where(a => a.CompanyId == companyId && attendanceIds.Contains(a.AttendanceId)).Select(a => new SaleCore
            {
                Id = a.Id,
                AttendanceId = a.AttendanceId,
                ProductId = a.ProductId,
                PTR = a.PTR,
                MRP = a.MRP,
                SaleValue = a.SaleValue,
                Discount = a.Discount,
                StockQuantity = a.StockQuantity,
                ReturnQuantity = a.ReturnQuantity,
                DistributorId = a.DistributorId,
                NearExpiryStockQuantity = a.NearExpiryStockQuantity,
                DistributorCashDiscount = a.DistributorCashDiscount,
                SchemeCashDiscount = a.SchemeCashDiscount,
                CGST = a.CGST,
                SGST = a.SGST,
                IGST = a.IGST,
                VAT = a.VAT,
                StandardUnitConversionFactor = a.StandardUnitConversionFactor,
                SuperUnitConversionFactor = a.SuperUnitConversionFactor,
            }).ToListAsync();
            return d;
        }
        public async Task<Dictionary<long, List<RetailerReturn>>> GetRetailerReturns(List<long> attendanceIds)
        {
            var d = await db.RetailerReturns.Where(a => attendanceIds.Contains(a.AttendanceId)).ToListAsync();
            return d.GroupBy(a => a.AttendanceId).ToDictionary(a => a.Key, a => a.Select(s => new RetailerReturn
            {
                ProductId = s.ProductId,
                ReturnQuantity = s.ReturnQuantity
            }).ToList());
        }

    }
}
