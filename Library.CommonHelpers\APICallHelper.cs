﻿using System;
using System.IO;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Threading.Tasks;
using Polly;
using Polly.Retry;

namespace Library.CommonHelpers
{
    public class APICallHelper
    {
        public static readonly PolicyBuilder retryPolicyBuilder = Policy
                    .Handle<HttpRequestException>()
                    .Or<TimeoutException>()
                    .Or<TaskCanceledException>();
        public static readonly AsyncRetryPolicy retryPolicy = retryPolicyBuilder
                    .WaitAndRetryAsync(5, retryNumber => TimeSpan.FromSeconds(Math.Pow(2, retryNumber - 1) * 30), onRetry: (exception, timespan, retryCount, _) =>
                    {
                        Console.WriteLine($"Task Failed. Retry attempt: {retryCount}");
                    });

        public static async Task<HttpResponseMessage> UploadFile(HttpClient httpClient, string uploadUrl, FileInfo file)
        {
            var fileStream = file.OpenRead();
            MultipartFormDataContent content = null;
            try
            {
                content = new MultipartFormDataContent();

                var fileContent = new StreamContent(fileStream);
                fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse("text/plain");

                content.Add(fileContent, "file", Path.GetFileName(file.Name));

                var response = (await retryPolicy.ExecuteAndCaptureAsync(async () => await httpClient.PostAsync(uploadUrl, content))).Result;

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("File uploaded successfully.");
                }
                else
                {
                    Console.WriteLine($"Upload failed. Status code: {response.StatusCode}");
                }
                return response;
            }
            finally
            {
                content?.Dispose();
            }
        }
    }
}
