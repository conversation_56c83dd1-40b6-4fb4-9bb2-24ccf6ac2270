﻿using Libraries.CommonEnums;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmbeddedEmails.DbStorage.DbModels
{
    public abstract class Manager
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public virtual long CompanyId { get; set; }
        public virtual bool IsDeactive { get; set; }
        public string EmailId { get; set; }
        public PortalUserRole UserRole { get; set; }
        public Guid? LoginGuid { get; set; }

        public abstract long UserId { get; }
        public bool IsSummarized { get;  set; }
        public bool IsPrimaryCategoryData { get; set; }
    }

    [Table("CompanyAdmins")]
    public class CompanyAdmin : Manager
    {
        [Column("Company")]
        public override long CompanyId { get; set; }

        public override long UserId => Id;
    }
}
