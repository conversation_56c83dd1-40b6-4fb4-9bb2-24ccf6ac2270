﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using System.Threading;
using ShareOrderonEmail.DbStorage.DbModels.MasterDbModels;

namespace ShareOrderonEmail.DbStorage.DbContexts
{
    public class MasterDbContext : DbContext
    {
        public MasterDbContext(DbContextOptions<MasterDbContext> options) : base(options)
        {

        }
        public DbSet<Company> Companies { get; set; }
        public DbSet<CompanySetting> CompanySettings { get; set; }
        public DbSet<CompanySettingValue> CompanySettingValues { get; set; }
        public DbSet<Distributor> Distributors { get; set; }
        public DbSet<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }
        public DbSet<Designations> Designations { get; set; }
        public DbSet<Employee> Employees { get; set; }
        public DbSet<VanDistributorMapping> VanDistributorMapping { get; set; }
        public DbSet<VanMaster> VanMaster { get; set; }
        public DbSet<LocationBeat> LocationBeats { get; set; }
        public DbSet<Location> Locations { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<ProductCESSCategory> ProductCESSCategories { get; set; }

        public DbSet<CESSCategoryTax> CESSCategoryTaxes { get; set; }
        public DbSet<ProductSecondaryCategory> ProductSecondaryCategories { get; set; }
        public DbSet<ProductPrimaryCategory> ProductPrimaryCategories { get; set; }
        public DbSet<GSTCategoryTax> GSTCategoryTaxes { get; set; }
        public DbSet<CompanyNomenclatureMapping> CompanyNomenclatureMapping { get; set; }
        public DbSet<CountryDetails> CountryDetails { get; set; }

        public DbSet<DistributorOrderPdfConfigs> DistributorOrderPdfConfigs { get; set; }

        public override int SaveChanges()
        {
            throw new NotImplementedException();
        }

        public override int SaveChanges(bool acceptAllChangesOnSuccess)
        {
            throw new NotImplementedException();
        }

        public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new NotImplementedException();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new NotImplementedException();
        }


        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {

        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Distributor>().ToTable("FADistributors");
            modelBuilder.Entity<Location>().ToTable("F2KLocations");

            modelBuilder.Entity<Product>().ToTable("FACompanyProducts");
            modelBuilder.Entity<ProductSecondaryCategory>().ToTable("FAProductSecondaryCategory");
            modelBuilder.Entity<ProductPrimaryCategory>().ToTable("FAProductPrimaryCategory");
            modelBuilder.Entity<Product>().Property(b => b.DisplayMRP).HasColumnName("MRP");
            modelBuilder.Entity<Product>().Property(b => b.MRP).HasColumnName("MRPNumeric");
            modelBuilder.Entity<Product>().Property(b => b.SecondaryCategoryId).HasColumnName("ProductCategoryId");

            modelBuilder.Entity<CompanyNomenclature>().ToTable("FACompanyNomenclatures").HasKey(d => d.Id);
            modelBuilder.Entity<CompanyNomenclatureMapping>().ToTable("FACompanyNomenclaturesMapping").HasKey(d => d.Id);
        }
    }
}
