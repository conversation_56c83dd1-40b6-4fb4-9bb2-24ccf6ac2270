﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace ShareOrderonEmail.DbStorage.DbModels.MasterDbModels
{
    public class Product
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public double Price { get; set; }
        public string DisplayCategory { get; set; }
        public long? ProductDisplayCategoryId { get; set; }
        public int? DisplayOrder { get; set; }
        public bool IsPromoted { get; set; }
        public string Unit { get; set; }
        public long? SecondaryCategoryId { get; set; }
        public string ImageId { get; set; }
        public string VariantName { get; set; }
        public decimal MRP { get; set; }
        public string DisplayMRP { get; set; }
        public double StandardUnitConversionFactor { set; get; }
        public string PackSize { get; set; }
        public DateTime LastUpdatedAt { get; internal set; }
        public bool IsActive { get; set; }
        public bool Deleted { set; get; }
        public long CompanyId { get; set; }
        public bool IsTopSelling { get; set; }
        public string LocalName { get; set; }
        public string Schemes { get; set; }
        public bool IsFocused { get; set; }
        public decimal? PricePlacement { get; set; }
        public decimal? PriceRegular { get; set; }
        public decimal? PriceSuperCash { get; set; }
        public bool IsAssorted { get; set; }
        public string ErpCode { get; set; }
        public double SuperUnitConversionFactor { get; set; }
        public decimal PTD { get; set; }
        public long? ProductGSTCategoryId { get; set; }
        public decimal? StandardUnitWeight { get; set; }
        public decimal? UnitWeight { get; set; }
        public double ProductWeightinGm { get; set; }
        public int? HSNCode { get; set; }
        public long? ProductCESSCategoryId { get; set; }
        public virtual ProductSecondaryCategory ProductCategory { get; set; }
    }

    public class ProductCESSCategory
    {
        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public long Id { get; set; }

        public bool Deleted { get; set; }

        [StringLength(256)]
        public string Name { get; set; }

        public long CompanyId { get; set; }

        public Company Company { get; set; }

        public IEnumerable<CESSCategoryTax> CESSCategoryTaxes { get; set; }
    }

    public class CESSCategoryTax
    {
        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public long Id { get; set; }

        public bool IsEnded { get; set; }

        public DateTime? EndedAt { get; set; }

        public decimal CESS { get; set; }

        public ProductCESSCategory CompanyCESSCategory { get; set; }

        public long CompanyCESSCategoryId { get; set; }

        public long CompanyId { get; set; }

        public Company Company { get; set; }
    }
    public partial class ProductSecondaryCategory
    {
        public long Id { get; set; }
        public long ProductPrimaryCategoryId { get; set; }
        public virtual ProductPrimaryCategory ProductPrimaryCategory { get; set; }
    }
    public partial class ProductPrimaryCategory 
    {
        public long Id { get; set; }
        public string StandardUnit { get; set; }
        public string SuperUnit { get; set; }
    }

    public class GSTCategoryTax
    {
        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public long Id { get; set; }

        public bool IsEnded { get; set; }

        public DateTime? EndedAt { get; set; }

        public decimal IGST { get; set; }
        public decimal CGST { get; set; }
        public decimal SGST { get; set; }
        public decimal VAT { get; set; }

        public long CompanyGSTCategoryId { get; set; }

        public long CompanyId { get; set; }
    }
}