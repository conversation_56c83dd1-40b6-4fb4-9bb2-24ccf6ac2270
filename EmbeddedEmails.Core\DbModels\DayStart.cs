﻿using System;
using System.Collections.Generic;
using System.Text;
using Libraries.CommonEnums;

namespace EmbeddedEmails.Core.DbModels
{
    public class DayStart
    {
        public Guid SessionId { get; set; }
        public long DayStartDateKey { get; set; }
        public double OrderInRevenue { get; set; }
        public double OrderInStdUnits { get; set; }
        public double LMTDBookingInRevenue { get; set; }
        public double MTDBookingInRevenue { get; set; }
        public double LMTDBookingInStdUnits { get; set; }
        public double MTDBookingInStdUnits { get; set; }
        public DayStartType DayStartType { get; set; }
        public int OVC { get; set; }
        public int TC { get; set; }
        public int PC { get; set; }
        public int SC { get; set; }
        public long ASMId { get; set; }
        public long ESMId { get; set; }
        public long RSMId { get; set; }
        public long ZSMId { get; set; }
        public long? NSMId { get; set; }
        public long? GSMId { get; set; }
        public long CompanyId { get; set; }
        public string ESMRank { get; set; }
        public long? AssignedBeatId { get; set; }
        public string AssignedReasonCategory { get; set; }
        public bool IsNormallyEnded { get; set; }
        public DateTime? DayEndTime { get; set; }
        public bool IsDayNotStarted { get; set; }
        public DateTime DayStartTime { get; set; }
        public DateTime? FirstCallTime { get; set; }
        public DateTime? FirstPCTime { get; set; }
        public bool IsLate { get; set; }
        public DateTime? LastCallTime { get; set; }
        public DateTime? LastPCTime { get; set; }
        public int LinesCut { get; set; }
        public double MTDBookingInUnits { get; set; }
        public int NewOutletsCreated { get; set; }
        public double NewOutletSalesInUnits { get; set; }
        public double NewOutletSalesInStdUnits { get; set; }
        public double NewOutletSalesInRevenue { get; set; }
        public double OrderInUnits { get; set; }
        public int NoOfStyles { get; set; }
        public int NoOfSecCategories { get; set; }
        public int OVT { get; set; }
        public string ReasonCategory { get; set; }
        public string Reason { get; set; }
        public int SchemeEffectiveCalls { get; set; }
        public int TelephonicOrders { get; set; }
        public long? SelectedBeatId { get; set; }
        public double TimeSpentRetailingMinutes { get; set; }
        public double ProductWiseDiscount { get; set; }
        public decimal TotalSchemeDiscount { get; set; }
        public double TotalSchemeQty { get; set; }
        public double TotalTimeMinutes { get; set; }
        public long? JointWorkingEmployeeId { get; set; }
        public long? AssignedJointWorkingEmployeeId { get; set; }
        public int JointWorkingCalls { get; set; }
        public string DayStartAddress { get; set; }
        public double MTDDispatchInRevenue { get; set; }
        public double MTDDispatchInUnits { get; set; }
        public double MTDDispatchInStdUnits { get; set; }
        public double LMTDDispatchInRevenue { get; set; }
        public double LMTDDispatchInUnits { get; set; }
        public double LMTDDispatchInStdUnits { get; set; }
    }
}
