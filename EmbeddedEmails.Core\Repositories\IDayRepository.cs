﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using EmbeddedEmails.Core.DbModels;
using Libraries.CommonEnums;

namespace EmbeddedEmails.Core.Repositories
{
    public interface IDayRepository
    {
        Task<List<DayStart>> GetDaySummary(long companyId, PortalUserRole userRole, long userId, long dateKey);
        Task<int> GetMaxActiveEmpForDayIndateRange(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey);
        Task<int> GetMaxScForDayIndateRange(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey);
        Task<int> GetMaxPcForDayIndateRange(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey);
        Task<int> GetMaxTcForDayIndateRange(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey);
        Task<List<DayStart>> GetDateRangeSummary(long companyId, PortalUserRole userRole, long userId, long fromDateKey, long toDateKey);
    }
}
