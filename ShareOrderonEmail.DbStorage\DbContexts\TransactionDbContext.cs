﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.EntityFrameworkCore;
using ShareOrderonEmail.DbStorage.DbModels.TransactionDbModels;

namespace ShareOrderonEmail.DbStorage.DbContexts
{
    public class TransactionDbContext : DbContext
    {
        public TransactionDbContext(DbContextOptions<TransactionDbContext> options) : base(options)
        {

        }
        public DbSet<Attendance> Attendances { get; set; }
        public DbSet<Sale> Sales { get; set; }
        public DbSet<DbRecievedPayment> DbRecievedPayment { get; set; }
        public DbSet<SchemeSale> SchemeSales { get; set; }
        public DbSet<SchemeSaleItem> SchemeSaleItems { get; set; }
        public DbSet<RetailerReturn> RetailerReturns { get; set; }
        public DbSet<VanSalesInvoiceDetails> VanSalesInvoiceDetails { get; set; }
        public override int SaveChanges()
        {
            throw new NotImplementedException();
        }
        public override int SaveChanges(bool acceptAllChangesOnSuccess)
        {
            throw new NotImplementedException();
        }


        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        { 
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
        }
    }
}
