<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Library.Infrastructure\Library.Infrastructure.csproj" />
    <ProjectReference Include="..\Library.CommonHelpers\Library.CommonHelpers.csproj" />
    <ProjectReference Include="..\Library.StorageWriter\Library.StorageWriter.csproj" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
    <PackageReference Include="SlackNet" Version="0.15.0" />
    <PackageReference Include="SlackNet.Extensions.DependencyInjection" Version="0.15.0" />
  </ItemGroup>

</Project>
